"""
测试GUI是否能正常弹出
"""

import sys
import threading
from update_fixed import UpdateManager

def test_gui_basic():
    """测试基本GUI功能"""
    print("测试基本GUI...")
    
    try:
        import tkinter as tk
        from tkinter import messagebox
        
        # 测试基本tkinter
        root = tk.Tk()
        root.withdraw()
        root.attributes('-topmost', True)
        
        result = messagebox.askyesno("测试", "GUI测试 - 能看到这个对话框吗？", parent=root)
        print(f"用户选择: {result}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"GUI测试失败: {e}")
        return False

def test_update_notification():
    """测试更新通知GUI"""
    print("测试更新通知...")
    
    try:
        updater = UpdateManager("gui_test", "1.0.0", auto_check=False)
        
        # 模拟更新信息
        updater.update_info = {
            "version": "2.0.0",
            "download_url": "/test/download",
            "release_notes": "测试更新说明\n- 新功能A\n- 修复Bug B"
        }
        
        print("准备显示更新通知...")
        updater._show_update_notification()
        
        return True
        
    except Exception as e:
        print(f"更新通知测试失败: {e}")
        return False

def test_custom_dialog():
    """测试自定义对话框"""
    print("测试自定义对话框...")
    
    try:
        import tkinter as tk
        
        updater = UpdateManager("dialog_test", "1.0.0", auto_check=False)
        
        root = tk.Tk()
        root.withdraw()
        
        message = "这是一个测试消息\n\n请选择操作："
        result = updater._show_custom_dialog(root, message)
        print(f"对话框结果: {result}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"自定义对话框测试失败: {e}")
        return False

def main():
    """运行GUI测试"""
    print("GUI功能测试")
    print("=" * 40)
    
    # 检查是否在主线程
    if threading.current_thread() is threading.main_thread():
        print("✓ 当前在主线程中")
    else:
        print("✗ 当前不在主线程中")
    
    tests = [
        test_gui_basic,
        test_update_notification,
        test_custom_dialog
    ]
    
    for i, test in enumerate(tests, 1):
        print(f"\n--- 测试 {i}: {test.__name__} ---")
        try:
            test()
        except Exception as e:
            print(f"测试异常: {e}")
        
        input("按回车继续下一个测试...")

if __name__ == "__main__":
    main()
