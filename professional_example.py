"""
专业级更新模块使用示例
展示跨平台GUI更新功能，支持ZIP包和单文件更新
"""

import time
import tkinter as tk
from tkinter import messagebox, ttk
from update import UpdateManager, check_update_once, start_update_checker


def example_1_simple_usage():
    """示例1: 最简单的使用方式 - 专业级GUI"""
    print("=== 示例1: 简单使用（专业级GUI） ===")
    
    # 方式1: 手动检查一次更新（自动显示专业GUI）
    has_update = check_update_once("your_software", "1.0.0")
    print(f"检查到更新: {has_update}")
    
    # 方式2: 启动后台定时检查（自动显示专业GUI）
    updater = start_update_checker("your_software", "1.0.0")
    print("后台更新检查已启动，会自动显示专业GUI通知")
    
    # 模拟程序运行
    time.sleep(5)
    
    # 停止后台检查
    updater.stop_background_check()
    print("后台更新检查已停止")


def example_2_tkinter_integration():
    """示例2: 与现有tkinter应用完美集成"""
    print("=== 示例2: tkinter应用集成 ===")
    
    class MyProfessionalApp:
        def __init__(self):
            self.root = tk.Tk()
            self.root.title("我的专业应用")
            self.root.geometry("400x300")
            
            # 创建界面
            title_frame = ttk.Frame(self.root, padding="20")
            title_frame.pack(fill=tk.X)
            
            ttk.Label(
                title_frame, 
                text="我的专业应用 v1.0.0",
                font=('Arial', 16, 'bold')
            ).pack()
            
            # 功能区域
            main_frame = ttk.Frame(self.root, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)
            
            ttk.Button(
                main_frame, 
                text="检查更新", 
                command=self.check_update,
                width=20
            ).pack(pady=10)
            
            ttk.Button(
                main_frame, 
                text="应用设置", 
                command=self.show_settings,
                width=20
            ).pack(pady=10)
            
            ttk.Button(
                main_frame, 
                text="关于", 
                command=self.show_about,
                width=20
            ).pack(pady=10)
            
            ttk.Button(
                main_frame, 
                text="退出", 
                command=self.root.quit,
                width=20
            ).pack(pady=20)
            
            # 状态栏
            self.status_var = tk.StringVar(value="就绪")
            status_frame = ttk.Frame(self.root)
            status_frame.pack(fill=tk.X, side=tk.BOTTOM)
            ttk.Label(status_frame, textvariable=self.status_var).pack(side=tk.LEFT, padx=10, pady=5)
            
            # 初始化专业级更新管理器
            self.updater = UpdateManager(
                software_name="my_professional_app",
                current_version="1.0.0",
                auto_check=True  # 自动启动后台检查
            )
            
            self.status_var.set("后台更新检查已启动")
        
        def check_update(self):
            """手动检查更新"""
            self.status_var.set("正在检查更新...")
            self.root.update()
            
            # 手动检查一次，会显示专业GUI
            has_update = self.updater.check_once()
            
            if not has_update:
                messagebox.showinfo(
                    "检查更新",
                    "当前已是最新版本！",
                    parent=self.root
                )
            
            self.status_var.set("就绪")
        
        def show_settings(self):
            """显示设置"""
            messagebox.showinfo("设置", "这里是应用设置界面", parent=self.root)
        
        def show_about(self):
            """显示关于"""
            messagebox.showinfo(
                "关于",
                "我的专业应用 v1.0.0\n\n"
                "集成了专业级自动更新功能\n"
                "支持Windows/Linux/macOS跨平台\n"
                "支持ZIP包和单文件更新",
                parent=self.root
            )
        
        def run(self):
            """运行应用"""
            try:
                self.root.mainloop()
            finally:
                # 确保停止后台检查
                self.updater.stop_background_check()
    
    # 运行应用
    app = MyProfessionalApp()
    app.run()


def example_3_console_app():
    """示例3: 控制台应用（自动降级到控制台通知）"""
    print("=== 示例3: 控制台应用 ===")
    
    class ConsoleApp:
        def __init__(self):
            # 初始化专业级更新管理器
            self.updater = UpdateManager(
                software_name="console_professional_app",
                current_version="1.0.0",
                auto_check=False  # 手动控制检查时机
            )
        
        def run(self):
            print("专业控制台应用启动")
            print("支持的命令:")
            print("  check  - 检查更新")
            print("  status - 显示状态")
            print("  quit   - 退出")
            
            while True:
                try:
                    cmd = input("\n> ").strip().lower()
                    
                    if cmd == 'check':
                        print("正在检查更新...")
                        # 会自动降级到控制台通知
                        self.updater.check_once()
                    
                    elif cmd == 'status':
                        print(f"应用: {self.updater.software_name}")
                        print(f"版本: {self.updater.current_version}")
                        print(f"平台: {self.updater._app_directory}")
                    
                    elif cmd == 'quit':
                        break
                    
                    else:
                        print("未知命令，请输入 check, status 或 quit")
                
                except (EOFError, KeyboardInterrupt):
                    break
            
            self.updater.stop_background_check()
            print("应用退出")
    
    app = ConsoleApp()
    app.run()


def example_4_advanced_features():
    """示例4: 高级功能展示"""
    print("=== 示例4: 高级功能 ===")
    
    # 自定义服务器和检查间隔
    updater = UpdateManager(
        software_name="advanced_professional_app",
        current_version="2.1.0",
        base_url="https://your-server.com",  # 自定义服务器
        check_interval=1800,  # 30分钟检查一次
        auto_check=False
    )
    
    print("高级功能演示:")
    print(f"软件名: {updater.software_name}")
    print(f"当前版本: {updater.current_version}")
    print(f"应用目录: {updater._app_directory}")
    print(f"当前可执行文件: {updater._current_executable}")
    
    # 只检查更新，不显示GUI
    print("\n检查更新（不显示GUI）...")
    update_available = updater.check_update()
    
    if update_available:
        print(f"发现新版本: {updater.update_info['version']}")
        print(f"文件类型: {updater.update_info.get('file_type', 'unknown')}")
        print(f"文件大小: {updater.update_info.get('file_size', 0)} 字节")
        print(f"更新说明: {updater.update_info['release_notes']}")
        
        # 可以选择是否显示GUI通知
        choice = input("是否显示GUI通知? (y/n): ").lower()
        if choice == 'y':
            updater._show_update_notification()
    
    elif update_available is False:
        print("当前已是最新版本")
    else:
        print("检查更新失败（可能是网络问题或服务器配置）")


def example_5_zip_update_simulation():
    """示例5: ZIP更新模拟"""
    print("=== 示例5: ZIP更新功能演示 ===")
    
    updater = UpdateManager(
        software_name="zip_update_demo",
        current_version="1.0.0",
        auto_check=False
    )
    
    # 模拟ZIP更新信息
    updater.update_info = {
        "version": "2.0.0",
        "download_url": "/demo/app_v2.zip",
        "release_notes": "重大更新:\n- 新增功能A\n- 优化性能\n- 修复已知问题",
        "file_size": 5242880,  # 5MB
        "file_type": "zip"
    }
    
    print("模拟ZIP更新场景:")
    print(f"当前版本: {updater.current_version}")
    print(f"新版本: {updater.update_info['version']}")
    print(f"更新类型: {updater.update_info['file_type'].upper()}")
    print(f"文件大小: {updater.update_info['file_size'] / 1024 / 1024:.1f} MB")
    print(f"更新说明: {updater.update_info['release_notes']}")
    
    print("\nZIP更新特点:")
    print("✓ 自动解压到应用目录")
    print("✓ 智能备份当前版本")
    print("✓ 跨平台权限设置")
    print("✓ 完整性验证")
    print("✓ 失败自动回滚")
    
    # 可以选择模拟下载（实际不会下载）
    choice = input("\n是否模拟显示下载界面? (y/n): ").lower()
    if choice == 'y':
        print("注意: 这会尝试显示下载界面，但由于没有真实文件会失败")
        try:
            updater._show_update_notification()
        except Exception as e:
            print(f"预期的错误: {e}")


def main():
    """主菜单"""
    print("专业级更新模块使用示例")
    print("支持Windows/Linux/macOS跨平台")
    print("=" * 50)
    print("请选择要运行的示例:")
    print("1. 简单使用（专业级GUI）")
    print("2. tkinter应用集成")
    print("3. 控制台应用")
    print("4. 高级功能展示")
    print("5. ZIP更新功能演示")
    print("0. 退出")
    
    while True:
        try:
            choice = input("\n请输入选择 (0-5): ").strip()
            
            if choice == "1":
                example_1_simple_usage()
            elif choice == "2":
                example_3_tkinter_integration()
            elif choice == "3":
                example_3_console_app()
            elif choice == "4":
                example_4_advanced_features()
            elif choice == "5":
                example_5_zip_update_simulation()
            elif choice == "0":
                print("退出示例程序")
                break
            else:
                print("无效选择，请输入 0-5")
                
        except (KeyboardInterrupt, EOFError):
            print("\n用户中断，退出程序")
            break
        except Exception as e:
            print(f"运行出错: {e}")


if __name__ == "__main__":
    main()
