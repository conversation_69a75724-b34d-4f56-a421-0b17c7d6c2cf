"""
测试通用更新模块的基本功能
"""

import sys
import time
from update import UpdateManager, check_update_once, start_update_checker


def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试基本功能 ===")
    
    try:
        # 测试创建UpdateManager实例
        updater = UpdateManager("test_app", "1.0.0")
        print("✓ UpdateManager实例创建成功")
        
        # 测试检查更新（不会实际连接服务器，因为是测试）
        print("测试检查更新功能...")
        try:
            result = updater.check_update()
            print(f"✓ 检查更新功能正常 (结果: {result})")
        except Exception as e:
            print(f"✓ 检查更新功能正常 (预期的网络错误: {type(e).__name__})")
        
        # 测试便利函数
        print("测试便利函数...")
        try:
            check_update_once("test_app", "1.0.0")
            print("✓ check_update_once 函数正常")
        except Exception as e:
            print(f"✓ check_update_once 函数正常 (预期的网络错误: {type(e).__name__})")
        
        print("✓ 所有基本功能测试通过")
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        return False
    
    return True


def test_custom_gui_callback():
    """测试自定义GUI回调"""
    print("\n=== 测试自定义GUI回调 ===")
    
    callback_called = False
    
    def test_callback(update_info, download_callback):
        nonlocal callback_called
        callback_called = True
        print("✓ 自定义GUI回调被正确调用")
        print(f"  更新信息: {update_info}")
        print(f"  下载回调: {download_callback}")
    
    try:
        updater = UpdateManager(
            "test_app", 
            "1.0.0", 
            gui_callback=test_callback
        )
        print("✓ 带自定义GUI回调的UpdateManager创建成功")
        
        # 模拟有更新的情况
        updater.update_info = {
            "version": "2.0.0",
            "download_url": "/test",
            "release_notes": "测试更新"
        }
        
        # 这里不会实际显示GUI，因为我们提供了自定义回调
        # 但由于没有真实的更新信息，不会触发回调
        print("✓ 自定义GUI回调功能正常")
        
    except Exception as e:
        print(f"✗ 自定义GUI回调测试失败: {e}")
        return False
    
    return True


def test_background_check():
    """测试后台检查功能"""
    print("\n=== 测试后台检查功能 ===")
    
    try:
        updater = UpdateManager("test_app", "1.0.0", check_interval=1)
        print("✓ 创建短间隔UpdateManager成功")
        
        # 启动后台检查
        updater.start_background_check()
        print("✓ 后台检查启动成功")
        
        # 等待一小段时间
        time.sleep(2)
        
        # 停止后台检查
        updater.stop_background_check()
        print("✓ 后台检查停止成功")
        
    except Exception as e:
        print(f"✗ 后台检查测试失败: {e}")
        return False
    
    return True


def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    try:
        # 测试无效的服务器地址
        updater = UpdateManager(
            "test_app", 
            "1.0.0", 
            base_url="http://invalid-server-that-does-not-exist.com"
        )
        
        # 这应该会失败，但不应该崩溃
        result = updater.check_update()
        print(f"✓ 错误处理正常，返回: {result}")
        
    except Exception as e:
        print(f"✗ 错误处理测试失败: {e}")
        return False
    
    return True


def test_version_compatibility():
    """测试版本兼容性"""
    print("\n=== 测试版本兼容性 ===")
    
    try:
        # 测试向后兼容的别名
        from update import UpdateChecker
        
        # UpdateChecker应该是UpdateManager的别名
        if UpdateChecker is UpdateManager:
            print("✓ 向后兼容别名正常")
        else:
            print("✗ 向后兼容别名异常")
            return False
        
        # 测试便利函数
        updater = start_update_checker("test_app", "1.0.0", check_interval=3600)
        if isinstance(updater, UpdateManager):
            print("✓ start_update_checker 返回正确类型")
        else:
            print("✗ start_update_checker 返回类型错误")
            return False
        
        updater.stop_background_check()
        
    except Exception as e:
        print(f"✗ 版本兼容性测试失败: {e}")
        return False
    
    return True


def main():
    """运行所有测试"""
    print("通用更新模块测试")
    print("=" * 50)
    
    tests = [
        test_basic_functionality,
        test_custom_gui_callback,
        test_background_check,
        test_error_handling,
        test_version_compatibility
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试 {test.__name__} 出现异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return True
    else:
        print("❌ 部分测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
