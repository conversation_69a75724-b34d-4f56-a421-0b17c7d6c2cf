"""
测试专业级更新模块的功能
支持Windows/Linux/macOS跨平台测试
"""

import sys
import time
import platform
import tempfile
import zipfile
from pathlib import Path
from update import (
    UpdateManager, check_update_once, start_update_checker, 
    PLATFORM, IS_WINDOWS, IS_LINUX, IS_MACOS,
    _is_executable, _get_app_directory, _get_temp_update_path, _is_zip_file
)


def test_platform_detection():
    """测试平台检测"""
    print("=== 测试平台检测 ===")
    
    try:
        print(f"当前平台: {PLATFORM}")
        print(f"Windows: {IS_WINDOWS}")
        print(f"Linux: {IS_LINUX}")
        print(f"macOS: {IS_MACOS}")
        
        # 验证平台检测正确性
        actual_platform = platform.system().lower()
        if PLATFORM == actual_platform:
            print("✓ 平台检测正确")
        else:
            print(f"✗ 平台检测错误: 期望 {actual_platform}, 实际 {PLATFORM}")
            return False
            
        return True
        
    except Exception as e:
        print(f"✗ 平台检测测试失败: {e}")
        return False


def test_path_functions():
    """测试路径相关函数"""
    print("\n=== 测试路径函数 ===")
    
    try:
        # 测试应用目录获取
        app_dir = _get_app_directory()
        print(f"应用目录: {app_dir}")
        if app_dir.exists():
            print("✓ 应用目录获取正确")
        else:
            print("✗ 应用目录不存在")
            return False
        
        # 测试临时更新路径
        temp_path = _get_temp_update_path("test.exe")
        print(f"临时更新路径: {temp_path}")
        print("✓ 临时路径生成正常")
        
        # 测试ZIP文件检测
        assert _is_zip_file("test.zip") == True
        assert _is_zip_file("test.exe") == False
        print("✓ ZIP文件检测正常")
        
        # 测试可执行文件检测
        is_exec = _is_executable()
        print(f"当前程序是否可执行: {is_exec}")
        print("✓ 可执行文件检测正常")
        
        return True
        
    except Exception as e:
        print(f"✗ 路径函数测试失败: {e}")
        return False


def test_professional_updater():
    """测试专业级更新管理器"""
    print("\n=== 测试专业级更新管理器 ===")
    
    try:
        # 测试创建UpdateManager实例（不自动启动）
        updater = UpdateManager("test_app", "1.0.0", auto_check=False)
        print("✓ 专业级UpdateManager实例创建成功")
        
        # 验证属性
        assert updater.software_name == "test_app"
        assert updater.current_version == "1.0.0"
        assert updater._app_directory == _get_app_directory()
        print("✓ 实例属性验证正确")
        
        # 测试检查更新（不会实际连接服务器）
        print("测试检查更新功能...")
        try:
            result = updater.check_update()
            print(f"✓ 检查更新功能正常 (结果: {result})")
        except Exception as e:
            print(f"✓ 检查更新功能正常 (预期的网络错误: {type(e).__name__})")
        
        return True
        
    except Exception as e:
        print(f"✗ 专业级更新管理器测试失败: {e}")
        return False


def test_zip_functionality():
    """测试ZIP功能"""
    print("\n=== 测试ZIP功能 ===")
    
    try:
        # 创建测试ZIP文件
        temp_dir = Path(tempfile.mkdtemp())
        test_zip = temp_dir / "test_update.zip"
        
        # 创建一些测试文件
        test_files = {
            "app.exe": b"fake exe content",
            "config.ini": b"[settings]\nversion=2.0.0",
            "lib/helper.dll": b"fake dll content"
        }
        
        with zipfile.ZipFile(test_zip, 'w') as zf:
            for file_path, content in test_files.items():
                zf.writestr(file_path, content)
        
        print(f"✓ 测试ZIP文件创建成功: {test_zip}")
        
        # 测试ZIP文件检测
        assert _is_zip_file(test_zip.name) == True
        print("✓ ZIP文件检测正确")
        
        # 测试解压功能
        extract_dir = temp_dir / "extracted"
        extract_dir.mkdir()
        
        with zipfile.ZipFile(test_zip, 'r') as zf:
            zf.extractall(extract_dir)
        
        # 验证解压结果
        assert (extract_dir / "app.exe").exists()
        assert (extract_dir / "config.ini").exists()
        assert (extract_dir / "lib" / "helper.dll").exists()
        print("✓ ZIP解压功能正常")
        
        # 清理
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)
        
        return True
        
    except Exception as e:
        print(f"✗ ZIP功能测试失败: {e}")
        return False


def test_gui_mode():
    """测试GUI模式（不实际显示GUI）"""
    print("\n=== 测试GUI模式 ===")
    
    try:
        # 测试GUI模式的UpdateManager
        updater = UpdateManager("gui_test_app", "1.0.0", auto_check=False)
        
        # 模拟更新信息
        updater.update_info = {
            "version": "2.0.0",
            "download_url": "/test/download",
            "release_notes": "测试更新说明",
            "file_size": 1024000,
            "file_type": "zip"
        }
        
        print("✓ GUI模式更新信息设置成功")
        
        # 测试GUI相关方法（不实际显示）
        try:
            # 这些方法在没有GUI环境时会优雅降级
            updater._show_console_notification()
            print("✓ 控制台降级通知正常")
        except Exception as e:
            print(f"控制台通知测试: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ GUI模式测试失败: {e}")
        return False


def test_convenience_functions():
    """测试便利函数"""
    print("\n=== 测试便利函数 ===")
    
    try:
        # 测试check_update_once
        try:
            result = check_update_once("convenience_test", "1.0.0")
            print(f"✓ check_update_once 函数正常 (结果: {result})")
        except Exception as e:
            print(f"✓ check_update_once 函数正常 (预期的网络错误: {type(e).__name__})")
        
        # 测试start_update_checker（立即停止）
        try:
            updater = start_update_checker("convenience_test", "1.0.0")
            print("✓ start_update_checker 函数正常")
            updater.stop_background_check()
            print("✓ 后台检查停止正常")
        except Exception as e:
            print(f"✓ start_update_checker 函数正常 (预期的网络错误: {type(e).__name__})")
        
        return True
        
    except Exception as e:
        print(f"✗ 便利函数测试失败: {e}")
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    try:
        # 测试无效的服务器地址
        updater = UpdateManager(
            "error_test", 
            "1.0.0", 
            base_url="http://invalid-server-that-does-not-exist.com",
            auto_check=False
        )
        
        # 这应该会失败，但不应该崩溃
        result = updater.check_update()
        print(f"✓ 错误处理正常，返回: {result}")
        
        return True
        
    except Exception as e:
        print(f"✗ 错误处理测试失败: {e}")
        return False


def main():
    """运行所有测试"""
    print("专业级更新模块测试")
    print("=" * 60)
    print(f"测试平台: {platform.system()} {platform.release()}")
    print(f"Python版本: {sys.version}")
    print("=" * 60)
    
    tests = [
        test_platform_detection,
        test_path_functions,
        test_professional_updater,
        test_zip_functionality,
        test_gui_mode,
        test_convenience_functions,
        test_error_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试 {test.__name__} 出现异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！专业级更新模块工作正常！")
        return True
    else:
        print("❌ 部分测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
