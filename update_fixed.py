"""
专业级软件更新模块 - 产品级修复版本
解决用户体验、安全性、业务逻辑等关键问题

使用方法：
    from update_fixed import UpdateManager

    # 手动控制检查
    updater = UpdateManager("your_software", "1.0.0", auto_check=False)
    updater.check_once()

    # 或启动后台检查
    updater.start_background_check()
"""

import os
import sys
import time
import threading
import subprocess
import tempfile
import requests
import logging
import atexit
import re
import shutil
import zipfile
import platform
import hashlib
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime, timedelta

# 简洁的日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('UpdateManager')

# 平台检测
IS_WINDOWS = platform.system().lower() == 'windows'

# 全局锁，防止多实例冲突
_update_lock = threading.Lock()
_active_updater = None


def _get_filename_from_cd(content_disposition: str) -> Optional[str]:
    """从Content-Disposition头中提取文件名"""
    if not content_disposition:
        return None
    fname = re.findall(r'filename=["\'](.*?)["\']|filename=([^;]+)', content_disposition)
    if fname:
        for groups in fname:
            for group in groups:
                if group:
                    return group.strip()
    return None


def _get_app_directory() -> Path:
    """获取应用程序目录"""
    if getattr(sys, 'frozen', False):
        return Path(sys.executable).parent
    else:
        return Path(sys.argv[0]).parent.resolve()


def _is_zip_file(filename: str) -> bool:
    """检查是否为ZIP文件"""
    return filename.lower().endswith('.zip')


def _is_same_file(filename: str) -> bool:
    """检查是否为同名文件"""
    current_name = Path(sys.argv[0]).name
    return filename.lower() == current_name.lower()


def _is_frozen() -> bool:
    """检查是否为打包后的可执行文件"""
    return getattr(sys, 'frozen', False)


def _validate_zip_path(zip_path: str) -> bool:
    """验证ZIP路径安全性，防止路径遍历攻击"""
    # 规范化路径
    normalized = os.path.normpath(zip_path)
    # 检查是否包含危险的路径遍历
    if '..' in normalized or normalized.startswith('/') or ':' in normalized:
        return False
    return True


def _create_backup(app_dir: Path) -> Optional[Path]:
    """创建应用备份"""
    try:
        backup_dir = app_dir / f"backup_{int(time.time())}"
        backup_dir.mkdir(exist_ok=True)

        # 只备份重要文件，避免备份过大
        important_extensions = {'.exe', '.dll', '.so', '.dylib', '.py', '.pyd'}

        for item in app_dir.iterdir():
            if item.is_file() and (item.suffix.lower() in important_extensions or item.name.startswith('config')):
                try:
                    shutil.copy2(item, backup_dir / item.name)
                except Exception:
                    pass  # 忽略单个文件备份失败

        logger.info(f"创建备份: {backup_dir}")
        return backup_dir
    except Exception as e:
        logger.warning(f"创建备份失败: {str(e)}")
        return None


def _try_delayed_replace(software_name: str = "软件更新") -> None:
    """检查并执行延迟替换"""
    app_dir = _get_app_directory()
    current_exe = Path(sys.argv[0])
    new_file = app_dir / f"{current_exe.name}.new"

    if new_file.exists() and _is_frozen():
        logger.info("检测到延迟升级文件，准备替换...")
        try:
            if new_file.stat().st_size == 0:
                new_file.unlink(missing_ok=True)
                return

            _create_replace_script(current_exe, new_file, software_name)

        except Exception as e:
            logger.error(f"延迟升级失败: {str(e)}")


def _create_replace_script(current_exe: Path, new_file: Path, software_name: str) -> None:
    """创建替换脚本"""
    temp_dir = Path(tempfile.mkdtemp())

    try:
        if IS_WINDOWS:
            script_file = temp_dir / "replace.bat"
            with open(script_file, "w", encoding="utf-8") as f:
                f.write("@echo off\n")
                f.write("echo 正在更新...\n")
                f.write("timeout /t 2 /nobreak > nul\n")
                f.write(f'copy /Y "{new_file}" "{current_exe}"\n')
                f.write(f'del /f /q "{new_file}"\n')
                f.write(f'start "" "{current_exe}"\n')
                f.write(f'rmdir /S /Q "{temp_dir}"\n')

            subprocess.Popen(["cmd", "/c", str(script_file)],
                           creationflags=subprocess.CREATE_NO_WINDOW)
        else:
            script_file = temp_dir / "replace.sh"
            with open(script_file, "w", encoding="utf-8") as f:
                f.write("#!/bin/bash\n")
                f.write("echo '正在更新...'\n")
                f.write("sleep 2\n")
                f.write(f"cp '{new_file}' '{current_exe}'\n")
                f.write(f"chmod +x '{current_exe}'\n")
                f.write(f"rm -f '{new_file}'\n")
                f.write(f"'{current_exe}' &\n")
                f.write(f"rm -rf '{temp_dir}'\n")

            script_file.chmod(0o755)
            subprocess.Popen(["/bin/bash", str(script_file)])

        os._exit(0)

    except Exception as e:
        logger.error(f"创建替换脚本失败: {str(e)}")
        shutil.rmtree(temp_dir, ignore_errors=True)


class UpdateManager:
    """专业级更新管理器 - 产品级修复版本"""

    def __init__(
        self,
        software_name: str,
        current_version: str,
        base_url: str = "https://unlockoko.com",
        check_interval: int = 3600,
        auto_check: bool = False  # 默认不自动检查
    ):
        """初始化更新管理器"""
        global _active_updater

        # 防止多实例冲突
        with _update_lock:
            if _active_updater is not None:
                logger.warning("已存在活跃的UpdateManager实例")
                _active_updater.stop_background_check()
            _active_updater = self

        self.software_name = software_name.lower()
        self.current_version = current_version
        self.base_url = base_url.rstrip('/')
        self.check_interval = check_interval
        self.update_info: Optional[Dict[str, Any]] = None
        self.running = True
        self.thread: Optional[threading.Thread] = None

        # 下载状态
        self._download_window = None
        self._download_cancelled = False

        # 用户体验优化
        self._last_check_time = None
        self._last_notification_time = None
        self._user_dismissed_version = None  # 用户已拒绝的版本

        logger.info(f"初始化更新管理器: {software_name} v{current_version}")
        atexit.register(self.stop_background_check)

        # 只有明确要求才自动启动
        if auto_check:
            self.start_background_check()

    def start_background_check(self) -> None:
        """启动后台检查"""
        if self.thread is None or not self.thread.is_alive():
            self.running = True
            self.thread = threading.Thread(target=self._check_periodically, daemon=True)
            self.thread.start()
            logger.info("启动后台更新检查")

    def stop_background_check(self) -> None:
        """停止后台检查"""
        self.running = False
        self._cancel_download()
        logger.info("停止后台更新检查")

    def _cancel_download(self) -> None:
        """取消下载"""
        self._download_cancelled = True
        if self._download_window:
            try:
                self._download_window.quit()
                self._download_window.destroy()
                self._download_window = None
            except Exception:
                pass

    def _check_periodically(self) -> None:
        """定时检查循环 - 优化用户体验"""
        while self.running:
            try:
                # 检查是否需要跳过（避免频繁骚扰用户）
                if self._should_skip_check():
                    logger.debug("跳过本次检查")
                else:
                    if self.check_update():
                        # 检查用户是否已拒绝此版本
                        if self._should_show_notification():
                            self._show_update_notification()
                            self._last_notification_time = datetime.now()
            except Exception as e:
                logger.error(f"检查更新失败: {str(e)}")

            # 等待下次检查
            for _ in range(self.check_interval):
                if not self.running:
                    break
                time.sleep(1)

    def _should_skip_check(self) -> bool:
        """判断是否应该跳过检查"""
        # 如果最近刚检查过，跳过
        if self._last_check_time:
            time_since_last = datetime.now() - self._last_check_time
            if time_since_last < timedelta(minutes=30):  # 至少间隔30分钟
                return True
        return False

    def _should_show_notification(self) -> bool:
        """判断是否应该显示通知"""
        if not self.update_info:
            return False

        # 如果用户已拒绝此版本，不再通知
        if self._user_dismissed_version == self.update_info['version']:
            return False

        # 如果最近刚通知过，不重复通知
        if self._last_notification_time:
            time_since_last = datetime.now() - self._last_notification_time
            if time_since_last < timedelta(hours=4):  # 至少间隔4小时
                return False

        return True

    def check_update(self) -> bool:
        """检查是否有更新 - 添加缓存机制"""
        try:
            self._last_check_time = datetime.now()

            url = f"{self.base_url}/api/check_update"
            params = {
                "software_name": self.software_name,
                "current_version": self.current_version
            }

            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()

            if data.get("code") == "100":
                update_data = data.get("data", {})
                need_update = update_data.get("need_update", False)

                if need_update:
                    self.update_info = {
                        "version": update_data.get("version", ""),
                        "download_url": update_data.get("download_url", ""),
                        "release_notes": update_data.get("release_notes", "")
                    }
                    logger.info(f"发现新版本: {self.update_info['version']}")
                    return True
                else:
                    return False
            else:
                logger.warning(f"服务器错误: {data.get('msg', '未知错误')}")
                return False

        except Exception as e:
            logger.error(f"检查更新失败: {str(e)}")
            return False

    def check_once(self) -> bool:
        """手动检查一次更新"""
        _try_delayed_replace(self.software_name)

        if self.check_update():
            self._show_update_notification()
            return True
        return False

    def _show_update_notification(self) -> None:
        """显示更新通知 - 改进用户体验"""
        try:
            import tkinter as tk
            from tkinter import messagebox

            # 创建根窗口
            root = tk.Tk()
            root.withdraw()
            root.title(f"{self.software_name} - 更新通知")
            root.attributes('-topmost', True)

            if IS_WINDOWS:
                root.attributes('-toolwindow', True)

            # 构建消息
            message = (
                f"发现新版本 {self.update_info['version']}\n\n"
                f"当前版本: {self.current_version}\n\n"
                f"更新说明:\n{self.update_info['release_notes']}\n\n"
                "请选择操作："
            )

            # 使用自定义对话框提供更多选项
            result = self._show_custom_dialog(root, message)
            root.destroy()

            if result == "download":
                self._start_download()
            elif result == "dismiss":
                # 用户选择暂时不更新此版本
                self._user_dismissed_version = self.update_info['version']
                logger.info(f"用户暂时拒绝更新版本: {self.update_info['version']}")

        except ImportError:
            self._show_console_notification()
        except Exception as e:
            logger.error(f"显示更新通知失败: {str(e)}")
            self._show_console_notification()

    def _show_custom_dialog(self, parent, message: str) -> str:
        """显示自定义对话框，提供更多选项"""
        try:
            import tkinter as tk
            from tkinter import ttk

            dialog = tk.Toplevel(parent)
            dialog.title(f"{self.software_name} - 发现新版本")
            dialog.geometry("400x300")
            dialog.resizable(False, False)
            dialog.attributes('-topmost', True)
            dialog.transient(parent)
            dialog.grab_set()

            # 居中显示
            dialog.update_idletasks()
            x = (dialog.winfo_screenwidth() - 400) // 2
            y = (dialog.winfo_screenheight() - 300) // 2
            dialog.geometry(f"400x300+{x}+{y}")

            result = {"action": "cancel"}

            # 消息文本
            text_frame = ttk.Frame(dialog, padding="20")
            text_frame.pack(fill=tk.BOTH, expand=True)

            text_widget = tk.Text(text_frame, wrap=tk.WORD, height=10, width=40)
            text_widget.insert(tk.END, message)
            text_widget.config(state=tk.DISABLED)
            text_widget.pack(fill=tk.BOTH, expand=True)

            # 按钮框架
            button_frame = ttk.Frame(dialog, padding="20")
            button_frame.pack(fill=tk.X)

            def on_download():
                result["action"] = "download"
                dialog.destroy()

            def on_dismiss():
                result["action"] = "dismiss"
                dialog.destroy()

            def on_cancel():
                result["action"] = "cancel"
                dialog.destroy()

            # 按钮
            ttk.Button(button_frame, text="立即下载", command=on_download).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="暂不更新", command=on_dismiss).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="取消", command=on_cancel).pack(side=tk.RIGHT, padx=5)

            dialog.protocol("WM_DELETE_WINDOW", on_cancel)
            dialog.wait_window()

            return result["action"]

        except Exception as e:
            logger.error(f"显示自定义对话框失败: {str(e)}")
            # 降级到简单对话框
            import tkinter as tk
            from tkinter import messagebox

            if messagebox.askyesno("发现新版本", message, parent=parent):
                return "download"
            else:
                return "dismiss"
