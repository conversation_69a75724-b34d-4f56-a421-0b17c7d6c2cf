# 专业级软件更新模块

一个专业级的Python软件自动更新模块，支持跨平台GUI更新、ZIP包安装和智能文件替换。

## 🚀 核心特性

- ✅ **专业级GUI**: 默认提供美观的更新通知和下载进度界面
- ✅ **跨平台支持**: 完美支持Windows/Linux/macOS三大平台
- ✅ **ZIP包更新**: 智能解压ZIP包并替换整个应用目录
- ✅ **单文件更新**: 支持可执行文件的延迟替换更新
- ✅ **智能备份**: 自动备份当前版本，支持失败回滚
- ✅ **置顶通知**: 更新通知窗口自动置顶，确保用户看到
- ✅ **进度显示**: 实时显示下载进度、速度和剩余时间
- ✅ **权限处理**: 自动处理Linux/macOS的可执行权限
- ✅ **错误处理**: 完善的异常处理和专业级日志记录
- ✅ **类型安全**: 完整的类型提示支持

## 🎯 快速开始

### 1. 最简单的使用方式（专业级GUI）

```python
from update import check_update_once, start_update_checker

# 手动检查一次更新（自动显示专业GUI）
check_update_once("your_software", "1.0.0")

# 启动后台定时检查（自动显示专业GUI通知）
updater = start_update_checker("your_software", "1.0.0")
```

### 2. 与现有tkinter应用集成

```python
import tkinter as tk
from update import UpdateManager

class MyApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("我的应用")

        # 创建界面...
        tk.Button(self.root, text="检查更新", command=self.check_update).pack()

        # 初始化专业级更新管理器
        self.updater = UpdateManager(
            software_name="my_app",
            current_version="1.0.0",
            auto_check=True  # 自动启动后台检查
        )

    def check_update(self):
        """手动检查更新 - 会显示专业GUI"""
        self.updater.check_once()

    def run(self):
        try:
            self.root.mainloop()
        finally:
            self.updater.stop_background_check()

app = MyApp()
app.run()
```

### 3. 控制台应用（自动降级）

```python
from update import UpdateManager

# 控制台应用会自动降级到控制台通知
updater = UpdateManager("console_app", "1.0.0")
updater.check_once()  # 会在控制台显示更新信息
```

## 📚 API 文档

### UpdateManager 类

专业级更新管理器，默认提供GUI模式。

#### 构造函数

```python
UpdateManager(
    software_name: str,           # 软件名称
    current_version: str,         # 当前版本号
    base_url: str = "https://unlockoko.com",  # 服务器地址
    check_interval: int = 3600,   # 检查间隔（秒）
    auto_check: bool = True       # 是否自动启动后台检查
)
```

#### 主要方法

- `check_once() -> bool`: 手动检查一次更新（显示专业GUI）
- `start_background_check()`: 启动后台定时检查
- `stop_background_check()`: 停止后台检查
- `check_update() -> Optional[bool]`: 检查是否有更新（不显示GUI）

#### 专业级特性

- **智能GUI**: 自动检测环境，GUI不可用时降级到控制台
- **ZIP包支持**: 自动识别ZIP文件并解压替换整个目录
- **跨平台兼容**: Windows批处理/Linux&macOS Shell脚本自动选择
- **权限管理**: 自动设置Linux/macOS可执行权限
- **备份机制**: 更新前自动备份，失败时可回滚

### 便利函数

```python
# 手动检查一次更新（专业级GUI）
check_update_once(software_name: str, current_version: str) -> bool

# 启动后台更新检查（专业级GUI）
start_update_checker(software_name: str, current_version: str) -> UpdateManager
```

## 🔧 服务器端配置

确保你的服务器实现了以下API：

### 检查更新 API

```
GET /api/check_update?software_name=xxx&current_version=xxx&platform=xxx
```

响应格式：
```json
{
    "code": "100",
    "msg": "检查更新成功",
    "data": {
        "need_update": true,
        "version": "2.0.0",
        "download_url": "/api/download/software_name",
        "release_notes": "重大更新:\n- 新增功能A\n- 优化性能\n- 修复已知问题",
        "file_size": 5242880,
        "file_type": "zip"
    }
}
```

### 下载文件 API

```
GET /api/download/software_name
```

返回文件流，支持：
- **Content-Disposition头**: 指定文件名
- **Content-Length头**: 文件大小（用于进度显示）
- **ZIP文件**: 自动解压替换整个应用目录
- **单文件**: 智能替换可执行文件

## 🔥 高级用法

### 自定义配置

```python
updater = UpdateManager(
    software_name="my_app",
    current_version="1.0.0",
    base_url="https://your-server.com",  # 自定义服务器
    check_interval=1800,  # 30分钟检查一次
    auto_check=False  # 手动控制检查时机
)
```

### 只检查不显示GUI

```python
updater = UpdateManager("my_app", "1.0.0", auto_check=False)
has_update = updater.check_update()

if has_update:
    print(f"发现新版本: {updater.update_info['version']}")
    print(f"文件类型: {updater.update_info['file_type']}")
    print(f"文件大小: {updater.update_info['file_size']} 字节")
    # 可以选择是否显示GUI
    updater._show_update_notification()
```

### ZIP包更新流程

1. **自动识别**: 根据文件扩展名识别ZIP包
2. **智能备份**: 自动备份当前版本到backup_目录
3. **安全解压**: 解压到临时目录后再复制
4. **权限设置**: 自动设置可执行权限（Linux/macOS）
5. **完整性验证**: 验证文件大小和解压结果
6. **失败回滚**: 出错时自动恢复备份

## ⚠️ 注意事项

### 跨平台兼容性
- **Windows**: 使用批处理脚本进行文件替换
- **Linux/macOS**: 使用Shell脚本进行文件替换
- **权限处理**: 自动处理可执行文件权限

### 更新机制
- **单文件更新**: 延迟替换，重启时生效
- **ZIP包更新**: 立即生效，无需重启
- **智能备份**: 自动创建backup_时间戳目录

### 安全性
- **文件完整性**: 验证下载文件大小
- **权限检查**: 确保有写入权限
- **网络安全**: 支持HTTPS，30秒超时
- **错误恢复**: 下载失败自动清理临时文件

### 性能优化
- **流式下载**: 不占用大量内存
- **后台检查**: 不阻塞主程序
- **智能缓存**: 避免重复下载

## 📦 依赖

- `requests`: HTTP请求库
- `tkinter`: GUI界面（Python标准库）
- `zipfile`: ZIP文件处理（Python标准库）
- `pathlib`: 路径处理（Python标准库）

## 📄 许可证

MIT License - 可自由用于商业和开源项目
