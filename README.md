# 通用软件更新模块

一个简洁、通用的Python软件自动更新模块，支持自动检查更新、下载和安装。

## 特性

- ✅ **简单易用**: 只需传入软件名和版本号即可使用
- ✅ **避免GUI冲突**: 支持自定义GUI回调，不会与现有tkinter应用冲突
- ✅ **自动更新**: 支持exe文件的自动替换更新
- ✅ **后台检查**: 支持定时后台检查更新
- ✅ **进度显示**: 支持下载进度显示
- ✅ **错误处理**: 完善的异常处理和日志记录
- ✅ **类型提示**: 完整的类型注解支持

## 快速开始

### 1. 最简单的使用方式

```python
from update import check_update_once, start_update_checker

# 手动检查一次更新
check_update_once("your_software", "1.0.0")

# 启动后台定时检查（每小时检查一次）
updater = start_update_checker("your_software", "1.0.0")
```

### 2. 避免tkinter冲突的使用方式

如果你的项目已经使用了tkinter，可以使用自定义GUI回调：

```python
from update import UpdateManager

def custom_update_notification(update_info, download_callback):
    """自定义更新通知函数"""
    print(f"发现新版本: {update_info['version']}")
    print(f"更新说明: {update_info['release_notes']}")
    
    # 使用你自己的GUI框架显示通知
    user_choice = your_gui_ask_user("是否下载更新?")
    
    if user_choice:
        # 开始下载，可以传入进度回调
        success = download_callback(
            lambda current, total: print(f"进度: {current}/{total}")
        )

# 创建带自定义GUI的更新管理器
updater = UpdateManager(
    software_name="your_software",
    current_version="1.0.0",
    gui_callback=custom_update_notification
)

# 启动后台检查
updater.start_background_check()
```

### 3. 控制台应用使用方式

```python
from update import UpdateManager

def console_notification(update_info, download_callback):
    print(f"发现新版本: {update_info['version']}")
    choice = input("是否下载? (y/n): ")
    
    if choice.lower() == 'y':
        def progress(current, total):
            percent = current / total * 100
            print(f"下载进度: {percent:.1f}%")
        
        download_callback(progress)

updater = UpdateManager(
    software_name="console_app",
    current_version="1.0.0",
    gui_callback=console_notification
)

updater.check_once()
```

## API 文档

### UpdateManager 类

主要的更新管理器类。

#### 构造函数

```python
UpdateManager(
    software_name: str,           # 软件名称
    current_version: str,         # 当前版本号
    base_url: str = "https://unlockoko.com",  # 服务器地址
    check_interval: int = 3600,   # 检查间隔（秒）
    gui_callback: Optional[Callable] = None   # 自定义GUI回调
)
```

#### 主要方法

- `check_once() -> bool`: 手动检查一次更新
- `start_background_check()`: 启动后台定时检查
- `stop_background_check()`: 停止后台检查
- `check_update() -> Optional[bool]`: 检查是否有更新（不显示GUI）
- `download_update(progress_callback=None) -> bool`: 下载更新

### 便利函数

```python
# 手动检查一次更新
check_update_once(software_name: str, current_version: str) -> bool

# 启动后台更新检查
start_update_checker(software_name: str, current_version: str) -> UpdateManager
```

## 服务器端配置

确保你的服务器实现了以下API：

### 检查更新 API

```
GET /api/check_update?software_name=xxx&current_version=xxx
```

响应格式：
```json
{
    "code": "100",
    "msg": "检查更新成功",
    "data": {
        "need_update": true,
        "version": "1.1.0",
        "download_url": "/api/download/software_name",
        "release_notes": "修复了一些bug"
    }
}
```

### 下载文件 API

```
GET /api/download/software_name
```

返回文件流，支持Content-Disposition头指定文件名。

## 高级用法

### 自定义服务器地址

```python
updater = UpdateManager(
    software_name="my_app",
    current_version="1.0.0",
    base_url="https://your-server.com"
)
```

### 自定义检查间隔

```python
updater = UpdateManager(
    software_name="my_app",
    current_version="1.0.0",
    check_interval=1800  # 30分钟检查一次
)
```

### 只检查不下载

```python
updater = UpdateManager("my_app", "1.0.0")
has_update = updater.check_update()

if has_update:
    print(f"发现新版本: {updater.update_info['version']}")
    # 自己决定是否下载
```

## 注意事项

1. **exe文件自更新**: 如果下载的文件与当前exe文件同名，会自动保存为`.new`文件，重启时自动替换
2. **权限要求**: exe文件自更新需要写入权限
3. **网络超时**: 默认网络超时为30秒
4. **日志记录**: 模块会自动记录详细的操作日志
5. **线程安全**: 后台检查使用独立线程，不会阻塞主程序

## 依赖

- `requests`: HTTP请求
- `tkinter`: GUI显示（可选，如果使用自定义GUI回调则不需要）

## 许可证

MIT License
