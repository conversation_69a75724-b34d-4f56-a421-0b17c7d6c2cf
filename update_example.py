"""
通用更新模块使用示例
展示如何在不同项目中集成更新功能
"""

import time
import tkinter as tk
from tkinter import messagebox
from update import UpdateManager, check_update_once, start_update_checker


def example_1_simple_usage():
    """示例1: 最简单的使用方式"""
    print("=== 示例1: 简单使用 ===")
    
    # 方式1: 手动检查一次更新
    has_update = check_update_once("your_software", "1.0.0")
    print(f"检查到更新: {has_update}")
    
    # 方式2: 启动后台定时检查
    updater = start_update_checker("your_software", "1.0.0")
    print("后台更新检查已启动")
    
    # 模拟程序运行
    time.sleep(5)
    
    # 停止后台检查
    updater.stop_background_check()
    print("后台更新检查已停止")


def example_2_custom_gui():
    """示例2: 使用自定义GUI回调，避免tkinter冲突"""
    print("=== 示例2: 自定义GUI ===")
    
    def custom_update_notification(update_info, download_callback):
        """自定义更新通知函数"""
        print(f"发现新版本: {update_info['version']}")
        print(f"更新说明: {update_info['release_notes']}")
        
        # 这里可以使用你自己的GUI框架（如PyQt、wxPython等）
        # 或者集成到现有的GUI中
        user_choice = input("是否下载更新? (y/n): ").lower() == 'y'
        
        if user_choice:
            print("开始下载...")
            success = download_callback(lambda current, total: 
                print(f"下载进度: {current}/{total} ({current/total*100:.1f}%)")
            )
            print(f"下载{'成功' if success else '失败'}")
    
    # 创建带自定义GUI的更新管理器
    updater = UpdateManager(
        software_name="your_software",
        current_version="1.0.0",
        gui_callback=custom_update_notification
    )
    
    # 手动检查一次
    updater.check_once()


def example_3_tkinter_integration():
    """示例3: 与现有tkinter应用集成"""
    print("=== 示例3: tkinter集成 ===")
    
    class MyApp:
        def __init__(self):
            self.root = tk.Tk()
            self.root.title("我的应用")
            self.root.geometry("300x200")
            
            # 创建界面
            tk.Label(self.root, text="我的应用 v1.0.0").pack(pady=20)
            
            tk.Button(
                self.root, 
                text="检查更新", 
                command=self.check_update
            ).pack(pady=10)
            
            tk.Button(
                self.root, 
                text="退出", 
                command=self.root.quit
            ).pack(pady=10)
            
            # 初始化更新管理器（使用自定义GUI）
            self.updater = UpdateManager(
                software_name="my_app",
                current_version="1.0.0",
                gui_callback=self.show_update_dialog
            )
            
            # 启动后台检查
            self.updater.start_background_check()
        
        def check_update(self):
            """手动检查更新"""
            self.updater.check_once()
        
        def show_update_dialog(self, update_info, download_callback):
            """自定义更新对话框"""
            result = messagebox.askyesno(
                "发现新版本",
                f"发现新版本 {update_info['version']}\n\n"
                f"更新说明:\n{update_info['release_notes']}\n\n"
                "是否现在下载?",
                parent=self.root
            )
            
            if result:
                # 使用默认的GUI下载（会显示进度条）
                download_callback()
        
        def run(self):
            self.root.mainloop()
            self.updater.stop_background_check()
    
    # 运行应用
    app = MyApp()
    app.run()


def example_4_console_app():
    """示例4: 控制台应用"""
    print("=== 示例4: 控制台应用 ===")
    
    def console_progress(current, total):
        """控制台进度显示"""
        if total > 0:
            percent = current / total * 100
            bar_length = 50
            filled_length = int(bar_length * current // total)
            bar = '█' * filled_length + '-' * (bar_length - filled_length)
            print(f'\r下载进度: |{bar}| {percent:.1f}% ({current}/{total})', end='')
            if current == total:
                print()  # 换行
    
    def console_notification(update_info, download_callback):
        """控制台更新通知"""
        print(f"\n发现新版本: {update_info['version']}")
        print(f"更新说明: {update_info['release_notes']}")
        
        choice = input("是否下载更新? (y/n): ").lower()
        if choice == 'y':
            print("开始下载...")
            success = download_callback(console_progress)
            print(f"下载{'成功' if success else '失败'}")
    
    # 创建控制台更新管理器
    updater = UpdateManager(
        software_name="console_app",
        current_version="1.0.0",
        gui_callback=console_notification
    )
    
    print("控制台应用启动")
    print("输入 'check' 检查更新，输入 'quit' 退出")
    
    while True:
        cmd = input("> ").strip().lower()
        if cmd == 'check':
            updater.check_once()
        elif cmd == 'quit':
            break
        else:
            print("未知命令")
    
    updater.stop_background_check()
    print("应用退出")


def example_5_advanced_usage():
    """示例5: 高级用法"""
    print("=== 示例5: 高级用法 ===")
    
    # 自定义服务器地址和检查间隔
    updater = UpdateManager(
        software_name="advanced_app",
        current_version="2.1.0",
        base_url="https://your-server.com",  # 自定义服务器
        check_interval=1800  # 30分钟检查一次
    )
    
    # 只检查更新，不显示GUI
    update_available = updater.check_update()
    if update_available:
        print(f"发现新版本: {updater.update_info['version']}")
        
        # 自定义下载逻辑
        def my_progress(current, total):
            print(f"下载进度: {current/1024/1024:.1f}MB / {total/1024/1024:.1f}MB")
        
        success = updater.download_update(my_progress)
        print(f"下载结果: {'成功' if success else '失败'}")
    else:
        print("当前已是最新版本")


if __name__ == "__main__":
    print("通用更新模块使用示例")
    print("请选择要运行的示例:")
    print("1. 简单使用")
    print("2. 自定义GUI")
    print("3. tkinter集成")
    print("4. 控制台应用")
    print("5. 高级用法")
    
    try:
        choice = input("请输入选择 (1-5): ").strip()
        
        if choice == "1":
            example_1_simple_usage()
        elif choice == "2":
            example_2_custom_gui()
        elif choice == "3":
            example_3_tkinter_integration()
        elif choice == "4":
            example_4_console_app()
        elif choice == "5":
            example_5_advanced_usage()
        else:
            print("无效选择")
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"运行出错: {e}")
