2025-05-26 21:51:44,689 - UpdateManager - INFO - 初始化专业级更新管理器: test_app v1.0.0 [windows]
2025-05-26 21:51:44,689 - UpdateManager - INFO - 应用目录: D:\update
2025-05-26 21:51:44,690 - UpdateManager - INFO - 检查更新: https://unlockoko.com/api/check_update [平台: windows]
2025-05-26 21:51:44,794 - UpdateManager - INFO - 服务器响应: {'code': '-1', 'msg': '软件 test_app 不存在'}
2025-05-26 21:51:44,795 - UpdateManager - WARNING - 服务器返回错误: 软件 test_app 不存在
2025-05-26 21:51:44,822 - UpdateManager - INFO - 初始化专业级更新管理器: gui_test_app v1.0.0 [windows]
2025-05-26 21:51:44,822 - UpdateManager - INFO - 应用目录: D:\update
2025-05-26 21:51:53,891 - UpdateManager - INFO - 初始化专业级更新管理器: convenience_test v1.0.0 [windows]
2025-05-26 21:51:53,893 - UpdateManager - INFO - 应用目录: D:\update
2025-05-26 21:51:53,894 - UpdateManager - INFO - 检查更新: https://unlockoko.com/api/check_update [平台: windows]
2025-05-26 21:51:54,036 - UpdateManager - INFO - 服务器响应: {'code': '-1', 'msg': '软件 convenience_test 不存在'}
2025-05-26 21:51:54,037 - UpdateManager - WARNING - 服务器返回错误: 软件 convenience_test 不存在
2025-05-26 21:51:54,038 - UpdateManager - INFO - 初始化专业级更新管理器: convenience_test v1.0.0 [windows]
2025-05-26 21:51:54,038 - UpdateManager - INFO - 应用目录: D:\update
2025-05-26 21:51:54,039 - UpdateManager - INFO - 开始定时检查更新...
2025-05-26 21:51:54,039 - UpdateManager - INFO - 启动后台定时检查线程
2025-05-26 21:51:54,039 - UpdateManager - INFO - 检查更新: https://unlockoko.com/api/check_update [平台: windows]
2025-05-26 21:51:54,040 - UpdateManager - INFO - 停止后台检查
2025-05-26 21:51:54,042 - UpdateManager - INFO - 初始化专业级更新管理器: error_test v1.0.0 [windows]
2025-05-26 21:51:54,043 - UpdateManager - INFO - 应用目录: D:\update
2025-05-26 21:51:54,043 - UpdateManager - INFO - 检查更新: http://invalid-server-that-does-not-exist.com/api/check_update [平台: windows]
2025-05-26 21:51:54,078 - UpdateManager - ERROR - 检查更新请求失败: HTTPConnectionPool(host='invalid-server-that-does-not-exist.com', port=80): Max retries exceeded with url: /api/check_update?software_name=error_test&current_version=1.0.0&platform=windows (Caused by NameResolutionError("<urllib3.connection.HTTPConnection object at 0x0000029B0C4C3220>: Failed to resolve 'invalid-server-that-does-not-exist.com' ([Errno 11001] getaddrinfo failed)"))
2025-05-26 21:51:54,079 - UpdateManager - INFO - 停止后台检查
2025-05-26 21:51:54,079 - UpdateManager - INFO - 停止后台检查
2025-05-26 21:51:54,079 - UpdateManager - INFO - 停止后台检查
2025-05-26 21:51:54,107 - UpdateManager - INFO - 开始下载: https://unlockoko.com/test/download
2025-05-26 21:51:54,110 - UpdateManager - INFO - 服务器响应: {'code': '-1', 'msg': '软件 convenience_test 不存在'}
2025-05-26 21:51:54,110 - UpdateManager - WARNING - 服务器返回错误: 软件 convenience_test 不存在
2025-05-26 21:51:54,110 - UpdateManager - WARNING - 检查更新失败
2025-05-26 21:51:54,172 - UpdateManager - ERROR - 下载失败: 404 Client Error: NOT FOUND for url: https://unlockoko.com/test/download
2025-05-26 21:51:54,264 - UpdateManager - ERROR - 处理下载错误失败: can't invoke "destroy" command: application has been destroyed
2025-05-26 21:52:24,735 - UpdateManager - INFO - 初始化专业级更新管理器: imei_tool v1.0.0 [windows]
2025-05-26 21:52:24,736 - UpdateManager - INFO - 应用目录: D:\update
2025-05-26 21:52:24,736 - UpdateManager - INFO - 开始定时检查更新...
2025-05-26 21:52:24,737 - UpdateManager - INFO - 启动后台定时检查线程
2025-05-26 21:52:24,737 - UpdateManager - INFO - 检查更新: https://unlockoko.com/api/check_update [平台: windows]
2025-05-26 21:52:24,737 - UpdateManager - INFO - 检查更新: https://unlockoko.com/api/check_update [平台: windows]
2025-05-26 21:52:24,807 - UpdateManager - INFO - 服务器响应: {'code': '100', 'data': {'download_url': '/api/download/imei_tool', 'need_update': True, 'release_notes': 'qqqq', 'version': '1.8'}, 'msg': '检查更新成功'}
2025-05-26 21:52:24,807 - UpdateManager - INFO - 发现新版本: 1.8 [类型: unknown, 大小: 0 字节]
2025-05-26 21:52:24,808 - UpdateManager - INFO - 检测到新版本，准备显示GUI通知...
2025-05-26 21:52:24,811 - UpdateManager - INFO - 服务器响应: {'code': '100', 'data': {'download_url': '/api/download/imei_tool', 'need_update': True, 'release_notes': 'qqqq', 'version': '1.8'}, 'msg': '检查更新成功'}
2025-05-26 21:52:24,811 - UpdateManager - INFO - 发现新版本: 1.8 [类型: unknown, 大小: 0 字节]
2025-05-26 21:52:24,812 - UpdateManager - INFO - 初始化专业级更新管理器: test_app v1.0.0 [windows]
2025-05-26 21:52:24,813 - UpdateManager - INFO - 应用目录: D:\update
2025-05-26 21:52:24,813 - UpdateManager - INFO - 检查更新: https://unlockoko.com/api/check_update [平台: windows]
2025-05-26 21:52:24,989 - UpdateManager - INFO - 服务器响应: {'code': '-1', 'msg': '软件 test_app 不存在'}
2025-05-26 21:52:24,990 - UpdateManager - INFO - 显示更新通知: 发现新版本 1.8
2025-05-26 21:52:24,990 - UpdateManager - WARNING - 服务器返回错误: 软件 test_app 不存在
2025-05-26 21:52:24,991 - UpdateManager - INFO - 初始化专业级更新管理器: test_app v1.0.0 [windows]
2025-05-26 21:52:24,992 - UpdateManager - INFO - 应用目录: D:\update
2025-05-26 21:52:24,992 - UpdateManager - INFO - 开始定时检查更新...
2025-05-26 21:52:24,992 - UpdateManager - INFO - 启动后台定时检查线程
2025-05-26 21:52:24,992 - UpdateManager - INFO - 检查更新: https://unlockoko.com/api/check_update [平台: windows]
2025-05-26 21:52:25,037 - UpdateManager - INFO - 服务器响应: {'code': '-1', 'msg': '软件 test_app 不存在'}
2025-05-26 21:52:25,038 - UpdateManager - WARNING - 服务器返回错误: 软件 test_app 不存在
2025-05-26 21:52:25,038 - UpdateManager - WARNING - 检查更新失败
2025-05-26 21:52:26,046 - UpdateManager - INFO - 开始定时检查更新...
2025-05-26 21:52:26,046 - UpdateManager - INFO - 检查更新: https://unlockoko.com/api/check_update [平台: windows]
2025-05-26 21:52:26,109 - UpdateManager - INFO - 服务器响应: {'code': '-1', 'msg': '软件 test_app 不存在'}
2025-05-26 21:52:26,110 - UpdateManager - WARNING - 服务器返回错误: 软件 test_app 不存在
2025-05-26 21:52:26,110 - UpdateManager - WARNING - 检查更新失败
2025-05-26 21:52:27,004 - UpdateManager - INFO - 停止后台检查
2025-05-26 21:52:27,004 - UpdateManager - INFO - 初始化专业级更新管理器: test_app v1.0.0 [windows]
2025-05-26 21:52:27,005 - UpdateManager - INFO - 应用目录: D:\update
2025-05-26 21:52:27,005 - UpdateManager - INFO - 开始定时检查更新...
2025-05-26 21:52:27,005 - UpdateManager - INFO - 启动后台定时检查线程
2025-05-26 21:52:27,005 - UpdateManager - INFO - 检查更新: http://invalid-server-that-does-not-exist.com/api/check_update [平台: windows]
2025-05-26 21:52:27,005 - UpdateManager - INFO - 检查更新: http://invalid-server-that-does-not-exist.com/api/check_update [平台: windows]
2025-05-26 21:52:27,018 - UpdateManager - ERROR - 检查更新请求失败: HTTPConnectionPool(host='invalid-server-that-does-not-exist.com', port=80): Max retries exceeded with url: /api/check_update?software_name=test_app&current_version=1.0.0&platform=windows (Caused by NameResolutionError("<urllib3.connection.HTTPConnection object at 0x000002D92EB27F40>: Failed to resolve 'invalid-server-that-does-not-exist.com' ([Errno 11001] getaddrinfo failed)"))
2025-05-26 21:52:27,018 - UpdateManager - ERROR - 检查更新请求失败: HTTPConnectionPool(host='invalid-server-that-does-not-exist.com', port=80): Max retries exceeded with url: /api/check_update?software_name=test_app&current_version=1.0.0&platform=windows (Caused by NameResolutionError("<urllib3.connection.HTTPConnection object at 0x000002D92EB6C310>: Failed to resolve 'invalid-server-that-does-not-exist.com' ([Errno 11001] getaddrinfo failed)"))
2025-05-26 21:52:27,018 - UpdateManager - WARNING - 检查更新失败
2025-05-26 21:52:27,020 - UpdateManager - INFO - 初始化专业级更新管理器: test_app v1.0.0 [windows]
2025-05-26 21:52:27,020 - UpdateManager - INFO - 应用目录: D:\update
2025-05-26 21:52:27,020 - UpdateManager - INFO - 开始定时检查更新...
2025-05-26 21:52:27,021 - UpdateManager - INFO - 启动后台定时检查线程
2025-05-26 21:52:27,021 - UpdateManager - INFO - 检查更新: https://unlockoko.com/api/check_update [平台: windows]
2025-05-26 21:52:27,021 - UpdateManager - INFO - 停止后台检查
2025-05-26 21:52:27,022 - UpdateManager - INFO - 停止后台检查
2025-05-26 21:52:27,023 - UpdateManager - INFO - 停止后台检查
2025-05-26 21:52:27,023 - UpdateManager - INFO - 停止后台检查
2025-05-26 21:52:27,023 - UpdateManager - INFO - 停止后台检查
2025-05-26 21:52:27,023 - UpdateManager - INFO - 停止后台检查
2025-05-26 21:52:32,858 - UpdateManager - INFO - 初始化专业级更新管理器: imei_tool v1.7 [windows]
2025-05-26 21:52:32,859 - UpdateManager - INFO - 应用目录: D:\update
2025-05-26 21:52:32,860 - UpdateManager - INFO - 开始定时检查更新...
2025-05-26 21:52:32,860 - UpdateManager - INFO - 检查更新: https://unlockoko.com/api/check_update [平台: windows]
2025-05-26 21:52:32,860 - UpdateManager - INFO - 启动后台定时检查线程
2025-05-26 21:52:32,934 - UpdateManager - INFO - 服务器响应: {'code': '100', 'data': {'download_url': '/api/download/imei_tool', 'need_update': True, 'release_notes': 'qqqq', 'version': '1.8'}, 'msg': '检查更新成功'}
2025-05-26 21:52:32,934 - UpdateManager - INFO - 发现新版本: 1.8 [类型: unknown, 大小: 0 字节]
2025-05-26 21:52:32,934 - UpdateManager - INFO - 检测到新版本，准备显示GUI通知...
2025-05-26 21:52:33,084 - UpdateManager - INFO - 显示更新通知: 发现新版本 1.8
2025-05-26 21:52:42,863 - UpdateManager - INFO - 停止后台检查
2025-05-26 21:53:07,016 - UpdateManager - INFO - 初始化专业级更新管理器: imei_tool v1.7 [windows]
2025-05-26 21:53:07,016 - UpdateManager - INFO - 应用目录: D:\update
2025-05-26 21:53:07,017 - UpdateManager - INFO - 开始定时检查更新...
2025-05-26 21:53:07,017 - UpdateManager - INFO - 检查更新: https://unlockoko.com/api/check_update [平台: windows]
2025-05-26 21:53:07,018 - UpdateManager - INFO - 启动后台定时检查线程
2025-05-26 21:53:07,068 - UpdateManager - INFO - 服务器响应: {'code': '100', 'data': {'download_url': '/api/download/imei_tool', 'need_update': True, 'release_notes': 'qqqq', 'version': '1.8'}, 'msg': '检查更新成功'}
2025-05-26 21:53:07,068 - UpdateManager - INFO - 发现新版本: 1.8 [类型: unknown, 大小: 0 字节]
2025-05-26 21:53:07,069 - UpdateManager - INFO - 检测到新版本，准备显示GUI通知...
2025-05-26 21:53:07,236 - UpdateManager - INFO - 显示更新通知: 发现新版本 1.8
2025-05-26 21:53:09,025 - UpdateManager - INFO - 用户选择: 暂不更新
2025-05-26 21:53:12,528 - UpdateManager - INFO - 停止后台检查
2025-05-26 21:53:12,529 - UpdateManager - INFO - 停止后台检查
2025-05-26 21:53:14,373 - UpdateManager - INFO - 初始化专业级更新管理器: imei_tool v1.7 [windows]
2025-05-26 21:53:14,373 - UpdateManager - INFO - 应用目录: D:\update
2025-05-26 21:53:14,374 - UpdateManager - INFO - 开始定时检查更新...
2025-05-26 21:53:14,374 - UpdateManager - INFO - 启动后台定时检查线程
2025-05-26 21:53:14,375 - UpdateManager - INFO - 检查更新: https://unlockoko.com/api/check_update [平台: windows]
2025-05-26 21:53:14,447 - UpdateManager - INFO - 服务器响应: {'code': '100', 'data': {'download_url': '/api/download/imei_tool', 'need_update': True, 'release_notes': 'qqqq', 'version': '1.8'}, 'msg': '检查更新成功'}
2025-05-26 21:53:14,447 - UpdateManager - INFO - 发现新版本: 1.8 [类型: unknown, 大小: 0 字节]
2025-05-26 21:53:14,447 - UpdateManager - INFO - 检测到新版本，准备显示GUI通知...
2025-05-26 21:53:14,634 - UpdateManager - INFO - 显示更新通知: 发现新版本 1.8
2025-05-26 21:53:16,747 - UpdateManager - INFO - 用户选择: 下载更新
2025-05-26 21:53:16,869 - UpdateManager - INFO - 开始下载: https://unlockoko.com/api/download/imei_tool
2025-05-26 21:53:16,933 - UpdateManager - INFO - 服务器返回文件名: IMEI_Tool.exe
2025-05-26 21:53:16,934 - UpdateManager - INFO - 开始下载到: D:\update\IMEI_Tool.exe
2025-05-26 21:53:16,934 - UpdateManager - INFO - 文件类型: 单文件
2025-05-26 21:53:16,934 - UpdateManager - INFO - 更新类型: 普通更新
2025-05-26 21:53:25,730 - UpdateManager - INFO - 下载出错，已删除未完成的文件: D:\update\IMEI_Tool.exe
2025-05-26 21:53:25,730 - UpdateManager - ERROR - 下载失败: main thread is not in main loop
2025-05-26 21:53:27,818 - UpdateManager - ERROR - 执行下载失败: main thread is not in main loop
2025-05-26 21:53:54,180 - UpdateManager - INFO - 初始化专业级更新管理器: imei_tool v1.7 [windows]
2025-05-26 21:53:54,180 - UpdateManager - INFO - 应用目录: D:\update
2025-05-26 21:53:54,182 - UpdateManager - INFO - 开始定时检查更新...
2025-05-26 21:53:54,182 - UpdateManager - INFO - 启动后台定时检查线程
2025-05-26 21:53:54,182 - UpdateManager - INFO - 检查更新: https://unlockoko.com/api/check_update [平台: windows]
2025-05-26 21:53:54,239 - UpdateManager - INFO - 服务器响应: {'code': '100', 'data': {'download_url': '/api/download/imei_tool', 'need_update': True, 'release_notes': 'qqqq', 'version': '1.8'}, 'msg': '检查更新成功'}
2025-05-26 21:53:54,239 - UpdateManager - INFO - 发现新版本: 1.8 [类型: unknown, 大小: 0 字节]
2025-05-26 21:53:54,240 - UpdateManager - INFO - 检测到新版本，准备显示GUI通知...
2025-05-26 21:53:54,374 - UpdateManager - INFO - 显示更新通知: 发现新版本 1.8
2025-05-26 21:53:56,168 - UpdateManager - INFO - 用户选择: 下载更新
2025-05-26 21:53:56,308 - UpdateManager - INFO - 开始下载: https://unlockoko.com/api/download/imei_tool
2025-05-26 21:53:56,365 - UpdateManager - INFO - 服务器返回文件名: IMEI_Tool.exe
2025-05-26 21:53:56,366 - UpdateManager - INFO - 开始下载到: D:\update\IMEI_Tool.exe
2025-05-26 21:53:56,366 - UpdateManager - INFO - 文件类型: 单文件
2025-05-26 21:53:56,366 - UpdateManager - INFO - 更新类型: 普通更新
2025-05-26 21:54:05,433 - UpdateManager - INFO - 下载出错，已删除未完成的文件: D:\update\IMEI_Tool.exe
2025-05-26 21:54:05,433 - UpdateManager - ERROR - 下载失败: main thread is not in main loop
2025-05-26 21:54:07,310 - UpdateManager - ERROR - 执行下载失败: main thread is not in main loop
2025-05-26 21:54:53,298 - UpdateManager - INFO - 初始化专业级更新管理器: test v1.0.0 [windows]
2025-05-26 21:54:53,299 - UpdateManager - INFO - 应用目录: D:\update
2025-05-26 21:54:53,299 - UpdateManager - INFO - 停止后台检查
