"""
测试最终修复版本的GUI功能
"""

import sys
from update_final import UpdateManager

def test_gui_notification():
    """测试GUI通知"""
    print("测试GUI通知功能...")
    
    try:
        updater = UpdateManager("gui_test", "1.0.0")
        
        # 模拟更新信息
        updater.update_info = {
            "version": "2.0.0",
            "download_url": "/test/download",
            "release_notes": "测试更新说明:\n- 修复GUI问题\n- 优化用户体验\n- 增强安全性"
        }
        
        print("准备显示更新通知GUI...")
        print("应该会弹出一个对话框，有三个选项：是/否/取消")
        
        # 显示通知
        updater._show_update_notification()
        
        print("GUI测试完成")
        
    except Exception as e:
        print(f"GUI测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_basic_messagebox():
    """测试基本messagebox"""
    print("测试基本messagebox...")
    
    try:
        import tkinter as tk
        from tkinter import messagebox
        
        root = tk.Tk()
        root.withdraw()
        root.attributes('-topmost', True)
        
        result = messagebox.askyesnocancel(
            "测试对话框",
            "这是一个测试对话框\n\n请选择:\n- 是: 继续\n- 否: 跳过\n- 取消: 退出",
            parent=root
        )
        
        print(f"用户选择结果: {result}")
        print(f"结果类型: {type(result)}")
        
        if result is True:
            print("用户选择了'是'")
        elif result is False:
            print("用户选择了'否'")
        elif result is None:
            print("用户选择了'取消'")
        
        root.destroy()
        
    except Exception as e:
        print(f"基本messagebox测试失败: {e}")

def main():
    """运行测试"""
    print("最终修复版GUI测试")
    print("=" * 40)
    
    print("1. 测试基本messagebox")
    test_basic_messagebox()
    
    input("\n按回车继续测试更新通知...")
    
    print("\n2. 测试更新通知GUI")
    test_gui_notification()
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
