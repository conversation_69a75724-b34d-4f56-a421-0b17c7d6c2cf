"""
专业级软件更新模块 - 真正修复版本
彻底解决界面卡死和进度条不更新问题

使用方法：
    from update_fixed_final import UpdateManager

    # 手动检查更新
    updater = UpdateManager("your_software", "1.0.0")
    updater.check_once()
"""

import os
import sys
import time
import threading
import subprocess
import tempfile
import requests
import logging
import atexit
import re
import shutil
import zipfile
import platform
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime, timedelta

# 简洁的日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('UpdateManager')

# 平台检测
IS_WINDOWS = platform.system().lower() == 'windows'

# 全局锁，防止多实例冲突
_update_lock = threading.Lock()
_active_updater = None


def _get_filename_from_cd(content_disposition: str) -> Optional[str]:
    """从Content-Disposition头中提取文件名"""
    if not content_disposition:
        return None
    fname = re.findall(r'filename=["\'](.*?)["\']|filename=([^;]+)', content_disposition)
    if fname:
        for groups in fname:
            for group in groups:
                if group:
                    return group.strip()
    return None


def _get_app_directory() -> Path:
    """获取应用程序目录"""
    try:
        if getattr(sys, 'frozen', False):
            return Path(sys.executable).parent.resolve()
        else:
            return Path(sys.argv[0]).parent.resolve()
    except Exception:
        return Path.cwd()


def _is_zip_file(filename: str) -> bool:
    """检查是否为ZIP文件"""
    return filename.lower().endswith('.zip')


def _is_same_file(filename: str) -> bool:
    """检查是否为同名文件"""
    try:
        current_name = Path(sys.argv[0]).name
        return filename.lower() == current_name.lower()
    except Exception:
        return False


def _is_frozen() -> bool:
    """检查是否为打包后的可执行文件"""
    return getattr(sys, 'frozen', False)


def _validate_zip_path(zip_path: str) -> bool:
    """验证ZIP路径安全性，防止路径遍历攻击"""
    try:
        normalized = os.path.normpath(zip_path)
        dangerous_patterns = ['..', '/', '\\', ':']
        if any(pattern in normalized for pattern in dangerous_patterns):
            return False
        if os.path.isabs(normalized):
            return False
        return True
    except Exception:
        return False


def _safe_path_quote(path: Path) -> str:
    """安全的路径引用，处理空格和特殊字符"""
    path_str = str(path)
    if IS_WINDOWS:
        return f'"{path_str}"'
    else:
        return path_str.replace(' ', '\\ ').replace('(', '\\(').replace(')', '\\)')


def _try_delayed_replace(software_name: str = "软件更新") -> None:
    """检查并执行延迟替换"""
    try:
        app_dir = _get_app_directory()
        current_exe = Path(sys.argv[0])
        new_file = app_dir / f"{current_exe.name}.new"

        if new_file.exists() and _is_frozen():
            logger.info("检测到延迟升级文件，准备替换...")

            if new_file.stat().st_size == 0:
                new_file.unlink(missing_ok=True)
                logger.warning("删除无效的延迟升级文件")
                return

            _create_replace_script(current_exe, new_file, software_name)

    except Exception as e:
        logger.error(f"延迟升级失败: {str(e)}")


def _create_replace_script(current_exe: Path, new_file: Path, software_name: str) -> None:
    """创建替换脚本"""
    temp_dir = Path(tempfile.mkdtemp())

    try:
        if IS_WINDOWS:
            script_file = temp_dir / "replace.bat"
            current_quoted = _safe_path_quote(current_exe)
            new_quoted = _safe_path_quote(new_file)
            temp_quoted = _safe_path_quote(temp_dir)

            with open(script_file, "w", encoding="utf-8") as f:
                f.write("@echo off\n")
                f.write(f"title {software_name} - 正在更新\n")
                f.write("echo 正在更新，请稍候...\n")
                f.write("timeout /t 3 /nobreak > nul\n")
                f.write(f"copy /Y {new_quoted} {current_quoted}\n")
                f.write(f"del /f /q {new_quoted}\n")
                f.write(f"start \"\" {current_quoted}\n")
                f.write(f"rmdir /S /Q {temp_quoted}\n")

            subprocess.Popen(["cmd", "/c", str(script_file)],
                           creationflags=subprocess.CREATE_NO_WINDOW)
        else:
            script_file = temp_dir / "replace.sh"
            current_quoted = _safe_path_quote(current_exe)
            new_quoted = _safe_path_quote(new_file)
            temp_quoted = _safe_path_quote(temp_dir)

            with open(script_file, "w", encoding="utf-8") as f:
                f.write("#!/bin/bash\n")
                f.write(f"echo '{software_name} - 正在更新...'\n")
                f.write("sleep 3\n")
                f.write(f"cp {new_quoted} {current_quoted}\n")
                f.write(f"chmod +x {current_quoted}\n")
                f.write(f"rm -f {new_quoted}\n")
                f.write(f"nohup {current_quoted} > /dev/null 2>&1 &\n")
                f.write(f"rm -rf {temp_quoted}\n")

            script_file.chmod(0o755)
            subprocess.Popen(["/bin/bash", str(script_file)])

        logger.info("启动替换脚本，程序即将退出")
        os._exit(0)

    except Exception as e:
        logger.error(f"创建替换脚本失败: {str(e)}")
        shutil.rmtree(temp_dir, ignore_errors=True)


class UpdateManager:
    """专业级更新管理器 - 真正修复版本"""

    def __init__(
        self,
        software_name: str,
        current_version: str,
        base_url: str = "https://unlockoko.com",
        check_interval: int = 3600
    ):
        """初始化更新管理器"""
        global _active_updater

        # 防止多实例冲突
        with _update_lock:
            if _active_updater is not None:
                logger.warning("已存在活跃的UpdateManager实例，停止旧实例")
                try:
                    _active_updater.stop_background_check()
                except Exception:
                    pass
            _active_updater = self

        self.software_name = software_name.lower()
        self.current_version = current_version
        self.base_url = base_url.rstrip('/')
        self.check_interval = check_interval
        self.update_info: Optional[Dict[str, Any]] = None
        self.running = True
        self.thread: Optional[threading.Thread] = None

        # 下载状态
        self._download_window = None
        self._download_cancelled = False
        self._download_thread = None
        self._gui_lock = threading.Lock()

        # 进度更新控制
        self._last_progress_update = 0
        self._progress_update_interval = 0.1  # 100ms更新一次

        # 用户体验优化
        self._last_check_time = None
        self._last_notification_time = None
        self._user_dismissed_version = None
        self._user_cancelled_session = False

        logger.info(f"初始化更新管理器: {software_name} v{current_version}")
        atexit.register(self.stop_background_check)

    def start_background_check(self) -> None:
        """启动后台检查"""
        if self.thread is None or not self.thread.is_alive():
            self.running = True
            self.thread = threading.Thread(target=self._check_periodically, daemon=True)
            self.thread.start()
            logger.info("启动后台更新检查")

    def stop_background_check(self) -> None:
        """停止后台检查"""
        self.running = False
        self._cancel_download()
        if self.thread and self.thread.is_alive():
            try:
                self.thread.join(timeout=1)
            except Exception:
                pass
        logger.info("停止后台更新检查")

    def _cancel_download(self) -> None:
        """安全取消下载"""
        self._download_cancelled = True

        # 等待下载线程结束
        if self._download_thread and self._download_thread.is_alive():
            try:
                self._download_thread.join(timeout=2)
            except Exception:
                pass

        # 关闭GUI窗口
        with self._gui_lock:
            if self._download_window:
                try:
                    self._download_window.quit()
                    self._download_window.destroy()
                    self._download_window = None
                except Exception:
                    pass

    def _check_periodically(self) -> None:
        """定时检查循环"""
        while self.running:
            try:
                if self._should_check() and self.check_update():
                    if self._should_show_notification():
                        self._show_update_notification()
                        self._last_notification_time = datetime.now()
            except Exception as e:
                logger.error(f"检查更新失败: {str(e)}")

            # 可中断的等待
            for _ in range(self.check_interval):
                if not self.running:
                    break
                time.sleep(1)

    def _should_check(self) -> bool:
        """判断是否应该检查"""
        if self._last_check_time:
            time_since_last = datetime.now() - self._last_check_time
            if time_since_last < timedelta(minutes=30):
                return False
        return True

    def _should_show_notification(self) -> bool:
        """判断是否应该显示通知"""
        if not self.update_info:
            return False

        # 用户在本次会话中取消过，不再通知
        if self._user_cancelled_session:
            return False

        # 用户拒绝了此版本，4小时内不再通知此版本
        if self._user_dismissed_version == self.update_info['version']:
            if self._last_notification_time:
                time_since_dismiss = datetime.now() - self._last_notification_time
                if time_since_dismiss < timedelta(hours=4):
                    return False
                else:
                    # 4小时后重置拒绝状态
                    self._user_dismissed_version = None

        # 任何版本的通知间隔至少1小时
        if self._last_notification_time:
            time_since_last = datetime.now() - self._last_notification_time
            if time_since_last < timedelta(hours=1):
                return False

        return True

    def check_update(self) -> bool:
        """检查是否有更新"""
        try:
            self._last_check_time = datetime.now()

            url = f"{self.base_url}/api/check_update"
            params = {
                "software_name": self.software_name,
                "current_version": self.current_version
            }

            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()

            if data.get("code") == "100":
                update_data = data.get("data", {})
                need_update = update_data.get("need_update", False)

                if need_update:
                    self.update_info = {
                        "version": update_data.get("version", ""),
                        "download_url": update_data.get("download_url", ""),
                        "release_notes": update_data.get("release_notes", "")
                    }
                    logger.info(f"发现新版本: {self.update_info['version']}")
                    return True
                else:
                    return False
            else:
                logger.warning(f"服务器错误: {data.get('msg', '未知错误')}")
                return False

        except Exception as e:
            logger.error(f"检查更新失败: {str(e)}")
            return False

    def check_once(self) -> bool:
        """手动检查一次更新"""
        _try_delayed_replace(self.software_name)

        if self.check_update():
            self._show_update_notification()
            return True
        return False

    def _show_update_notification(self) -> None:
        """显示更新通知"""
        try:
            # 检查是否已有GUI窗口
            with self._gui_lock:
                if self._download_window:
                    logger.warning("已有下载窗口，跳过通知")
                    return

            import tkinter as tk
            from tkinter import messagebox

            # 创建独立的根窗口
            notification_root = tk.Tk()
            notification_root.withdraw()
            notification_root.title(f"{self.software_name} - 更新通知")
            notification_root.attributes('-topmost', True)

            if IS_WINDOWS:
                notification_root.attributes('-toolwindow', True)

            try:
                # 构建消息
                message = (
                    f"发现新版本 {self.update_info['version']}\n\n"
                    f"当前版本: {self.current_version}\n\n"
                    f"更新说明:\n{self.update_info['release_notes']}\n\n"
                    "请选择操作："
                )

                # 显示对话框
                user_choice = messagebox.askyesnocancel(
                    f"{self.software_name} - 发现新版本",
                    message,
                    parent=notification_root
                )

                # 处理用户选择
                if user_choice is True:
                    # 用户选择"是" - 立即下载
                    logger.info("用户选择立即下载更新")
                    self._start_download()
                elif user_choice is False:
                    # 用户选择"否" - 暂不更新此版本，但继续后台检测
                    self._user_dismissed_version = self.update_info['version']
                    logger.info(f"用户暂时拒绝更新版本: {self.update_info['version']}，4小时后再次提醒")
                elif user_choice is None:
                    # 用户选择"取消" - 本次会话不再提醒任何更新
                    self._user_cancelled_session = True
                    logger.info("用户取消更新通知，本次会话不再提醒")

            finally:
                # 确保清理根窗口
                try:
                    notification_root.destroy()
                except Exception:
                    pass

        except ImportError:
            logger.warning("tkinter不可用，使用控制台通知")
            self._show_console_notification()
        except Exception as e:
            logger.error(f"显示更新通知失败: {str(e)}")
            self._show_console_notification()

    def _show_console_notification(self) -> None:
        """控制台通知"""
        print(f"\n{'='*50}")
        print(f"🔔 {self.software_name} 更新通知")
        print(f"{'='*50}")
        print(f"发现新版本: {self.update_info['version']}")
        print(f"当前版本: {self.current_version}")
        print(f"更新说明: {self.update_info['release_notes']}")
        print(f"{'='*50}")

        try:
            print("请选择操作:")
            print("1. 立即下载")
            print("2. 暂不更新（4小时后再提醒）")
            print("3. 取消（本次会话不再提醒）")

            choice = input("请输入选择 (1-3): ").strip()
            if choice == "1":
                self._start_download()
            elif choice == "2":
                self._user_dismissed_version = self.update_info['version']
                print("已暂时跳过此版本更新，4小时后再次提醒")
            elif choice == "3":
                self._user_cancelled_session = True
                print("已取消更新通知，本次会话不再提醒")
        except (EOFError, KeyboardInterrupt):
            self._user_cancelled_session = True
            print("已取消更新通知")
