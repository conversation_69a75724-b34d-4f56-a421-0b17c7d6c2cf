"""
专业级通用软件更新模块
支持Windows/Linux/macOS跨平台自动更新
特性：
- 自动检查更新并弹出GUI通知
- 支持单文件和ZIP包更新
- 智能文件替换和目录更新
- 跨平台兼容性
- 专业级错误处理和日志

使用方法：
    from update import UpdateManager

    # 启动后台检查（默认GUI模式）
    updater = UpdateManager("your_software", "1.0.0")
    updater.start_background_check()

    # 手动检查一次
    updater.check_once()
"""

import os
import sys
import time
import threading
import subprocess
import tempfile
import requests
import logging
import atexit
import re
import shutil
import zipfile
import platform
import stat
from pathlib import Path
from typing import Optional, Callable, Dict, Any, List

# 配置专业级日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('update.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('UpdateManager')

# 平台检测
PLATFORM = platform.system().lower()
IS_WINDOWS = PLATFORM == 'windows'
IS_LINUX = PLATFORM == 'linux'
IS_MACOS = PLATFORM == 'darwin'


# 工具函数
def _is_exe() -> bool:
    """检查当前程序是否为exe文件"""
    return sys.argv[0].lower().endswith('.exe')


def _get_filename_from_cd(content_disposition: str) -> Optional[str]:
    """从Content-Disposition头中提取文件名"""
    if not content_disposition:
        return None
    fname = re.findall(r'filename=["\'](.*?)["\']|filename=([^;]+)', content_disposition)
    if fname:
        for groups in fname:
            for group in groups:
                if group:
                    return group.strip()
    return None


def _get_update_file_path() -> str:
    """获取更新文件的路径"""
    exe_path = os.path.abspath(sys.argv[0])
    if _is_exe():
        return exe_path + ".new"
    base, ext = os.path.splitext(exe_path)
    return f"{base}_new{ext}"


def _try_delayed_replace(software_name: str = "软件更新") -> None:
    """尝试执行延迟替换（程序启动时检查）"""
    exe_path = os.path.abspath(sys.argv[0])
    new_path = _get_update_file_path()

    if _is_exe() and os.path.exists(new_path):
        logger.info(f"检测到延迟升级文件: {new_path}，准备自动替换主程序...")
        try:
            # 检查新文件是否有效
            if os.path.getsize(new_path) == 0:
                logger.error(f"延迟升级文件无效(大小为0): {new_path}")
                try:
                    os.remove(new_path)
                    logger.info("已删除无效的延迟升级文件")
                except Exception:
                    pass
                return

            # 创建批处理文件执行替换
            temp_dir = tempfile.mkdtemp()
            batch_file = os.path.join(temp_dir, "replace.bat")
            with open(batch_file, "w", encoding="utf-8") as f:
                f.write("@echo off\n")
                f.write("echo 正在自动升级...\n")
                f.write("timeout /t 2 /nobreak > nul\n")
                f.write(f'copy /Y "{new_path}" "{exe_path}"\n')
                f.write(f'del /f /q "{new_path}"\n')
                f.write(f'start "" "{exe_path}"\n')
                f.write(f'rmdir /S /Q "{temp_dir}"\n')

            logger.info(f"执行延迟升级批处理: {batch_file}")
            subprocess.Popen(
                ["cmd", "/c", batch_file],
                shell=True,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            os._exit(0)
        except Exception as e:
            logger.error(f"延迟升级替换失败: {str(e)}")


class UpdateManager:
    """通用更新管理器"""

    def __init__(
        self,
        software_name: str,
        current_version: str,
        base_url: str = "https://unlockoko.com",
        check_interval: int = 3600,
        gui_callback: Optional[Callable] = None
    ):
        """
        初始化更新管理器

        Args:
            software_name: 软件名称
            current_version: 当前版本号
            base_url: 服务器地址
            check_interval: 检查间隔（秒）
            gui_callback: 自定义GUI回调函数，用于避免tkinter冲突
        """
        self.software_name = software_name.lower()
        self.current_version = current_version
        self.base_url = base_url.rstrip('/')
        self.check_interval = check_interval
        self.gui_callback = gui_callback
        self.update_info: Optional[Dict[str, Any]] = None
        self.running = True
        self.thread: Optional[threading.Thread] = None
        self._main_program_path = os.path.abspath(sys.argv[0])

        logger.info(f"初始化更新管理器: {software_name} v{current_version}")
        atexit.register(self.stop_background_check)

    def start_background_check(self) -> None:
        """启动后台定时检查"""
        if self.thread is None or not self.thread.is_alive():
            self.running = True
            self.thread = threading.Thread(target=self._check_periodically, daemon=True)
            self.thread.start()
            logger.info("启动后台定时检查线程")

    def stop_background_check(self) -> None:
        """停止后台检查"""
        self.running = False
        logger.info("停止后台检查")

    def _check_periodically(self) -> None:
        """定时检查循环"""
        while self.running:
            try:
                logger.info("开始定时检查更新...")
                update_available = self.check_update()
                if update_available is True:
                    logger.info("检测到新版本，准备通知用户...")
                    self._notify_update_available()
                elif update_available is False:
                    logger.info("当前已是最新版本")
                else:
                    logger.warning("检查更新失败")
            except Exception as e:
                logger.error(f"定时检查更新时出错: {str(e)}")

            # 可中断的等待
            for _ in range(self.check_interval):
                if not self.running:
                    break
                time.sleep(1)

    def check_update(self) -> Optional[bool]:
        """
        检查是否有更新

        Returns:
            True: 有更新
            False: 无更新
            None: 检查失败
        """
        try:
            url = f"{self.base_url}/api/check_update"
            params = {
                "software_name": self.software_name,
                "current_version": self.current_version
            }
            logger.info(f"检查更新: {url}")

            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()

            logger.info(f"服务器响应: {data}")

            if data.get("code") == "100":
                update_data = data.get("data", {})
                need_update = update_data.get("need_update", False)

                if need_update:
                    self.update_info = {
                        "version": update_data.get("version", ""),
                        "download_url": update_data.get("download_url", ""),
                        "release_notes": update_data.get("release_notes", "")
                    }
                    logger.info(f"发现新版本: {self.update_info['version']}")
                    return True
                else:
                    return False
            else:
                logger.warning(f"服务器返回错误: {data.get('msg', '未知错误')}")
                return None

        except Exception as e:
            logger.error(f"检查更新请求失败: {str(e)}")
            return None

    def check_once(self) -> bool:
        """
        手动检查一次更新，如果有更新则显示通知

        Returns:
            True: 检查到更新并显示了通知
            False: 无更新或检查失败
        """
        _try_delayed_replace(self.software_name)

        if self.check_update():
            self._notify_update_available()
            return True
        return False

    def _notify_update_available(self) -> None:
        """通知用户有更新可用"""
        if self.gui_callback:
            # 使用自定义GUI回调
            try:
                self.gui_callback(self.update_info, self.download_update)
            except Exception as e:
                logger.error(f"自定义GUI回调失败: {str(e)}")
        else:
            # 使用默认的tkinter通知
            self._show_default_notification()

    def _show_default_notification(self) -> None:
        """显示默认的tkinter更新通知"""
        def show_dialog():
            try:
                import tkinter as tk
                from tkinter import messagebox

                root = tk.Tk()
                root.withdraw()
                root.title(self.software_name)
                root.attributes('-topmost', True)

                logger.info(f"弹窗通知用户: 发现新版本 {self.update_info['version']}")

                user_choice = messagebox.askyesno(
                    self.software_name,
                    f"发现新版本 {self.update_info['version']}\n\n"
                    f"更新说明:\n{self.update_info['release_notes']}\n\n"
                    "是否现在更新?",
                    parent=root
                )

                logger.info(f"用户选择: {'更新' if user_choice else '暂不更新'}")

                if user_choice:
                    root.destroy()
                    self.download_update()
                else:
                    root.destroy()

            except ImportError:
                logger.warning("tkinter不可用，无法显示GUI通知")
            except Exception as e:
                logger.error(f"显示更新通知失败: {str(e)}")

        if threading.current_thread() is threading.main_thread():
            show_dialog()
        else:
            try:
                import tkinter as tk
                root = tk.Tk()
                root.withdraw()
                root.title(self.software_name)
                root.attributes('-topmost', True)
                root.after_idle(show_dialog)
                root.mainloop()
            except Exception as e:
                logger.error(f"显示更新通知失败: {str(e)}")

    def download_update(self, progress_callback: Optional[Callable] = None) -> bool:
        """
        下载并安装更新

        Args:
            progress_callback: 进度回调函数，接收 (current, total) 参数

        Returns:
            True: 下载成功
            False: 下载失败
        """
        if not self.update_info or not self.update_info.get("download_url"):
            logger.error("没有可用的更新信息")
            return False

        if progress_callback is None:
            # 使用默认的GUI进度显示
            return self._download_with_gui()
        else:
            # 使用自定义进度回调
            return self._download_with_callback(progress_callback)

    def _download_with_gui(self) -> bool:
        """使用GUI显示下载进度"""
        try:
            import tkinter as tk
            from tkinter import messagebox, ttk

            download_window = tk.Tk()
            download_window.title(self.software_name)
            download_window.geometry("400x150")
            download_window.resizable(False, False)
            download_window.attributes('-topmost', True)

            # 居中显示
            window_width, window_height = 400, 150
            screen_width = download_window.winfo_screenwidth()
            screen_height = download_window.winfo_screenheight()
            x = int(screen_width / 2 - window_width / 2)
            y = int(screen_height / 2 - window_height / 2)
            download_window.geometry(f"{window_width}x{window_height}+{x}+{y}")

            frame = tk.Frame(download_window, padx=20, pady=20)
            frame.pack(fill=tk.BOTH, expand=True)

            status_label = tk.Label(frame, text=f"正在下载 v{self.update_info['version']}...")
            status_label.pack(pady=(0, 10))

            progress = ttk.Progressbar(frame, orient="horizontal", length=360, mode="determinate")
            progress.pack(pady=(0, 10))

            info_label = tk.Label(frame, text="准备下载...")
            info_label.pack()

            stop_flag = threading.Event()
            download_success = threading.Event()

            def update_progress_callback(current, total):
                try:
                    if total > 0 and download_window.winfo_exists():
                        percentage = int(current / total * 100)
                        progress["value"] = percentage
                        info_label.config(
                            text=f"已下载: {current / 1024 / 1024:.2f} MB / {total / 1024 / 1024:.2f} MB ({percentage}%)")
                        download_window.update_idletasks()
                except Exception:
                    # 窗口已关闭，忽略更新进度的错误
                    pass

            def download_thread():
                try:
                    success = self._download_file(
                        lambda c, t: download_window.after_idle(lambda: update_progress_callback(c, t)),
                        stop_flag
                    )
                    if success:
                        download_success.set()
                        if download_window.winfo_exists():
                            download_window.after_idle(lambda: status_label.config(text="下载完成！"))
                            download_window.after_idle(self._handle_download_complete)
                    else:
                        if download_window.winfo_exists():
                            download_window.after_idle(
                                lambda: messagebox.showerror(self.software_name, "下载失败", parent=download_window)
                            )
                            download_window.after_idle(download_window.destroy)
                except Exception as e:
                    logger.error(f"下载失败: {str(e)}")
                    if download_window.winfo_exists():
                        download_window.after_idle(
                            lambda: messagebox.showerror(self.software_name, f"下载失败:\n{str(e)}", parent=download_window)
                        )
                        download_window.after_idle(download_window.destroy)

            def on_close():
                if messagebox.askokcancel(self.software_name, "正在下载，确定要取消吗？", parent=download_window):
                    logger.warning("用户取消了下载")
                    stop_flag.set()
                    download_window.destroy()

            download_window.protocol("WM_DELETE_WINDOW", on_close)
            threading.Thread(target=download_thread, daemon=True).start()
            download_window.mainloop()

            return download_success.is_set()

        except ImportError:
            logger.warning("tkinter不可用，使用命令行下载")
            return self._download_with_callback(lambda c, t: logger.info(f"下载进度: {c}/{t}"))
        except Exception as e:
            logger.error(f"GUI下载失败: {str(e)}")
            return False

    def _download_with_callback(self, progress_callback: Callable) -> bool:
        """使用回调函数显示下载进度"""
        try:
            return self._download_file(progress_callback, threading.Event())
        except Exception as e:
            logger.error(f"下载失败: {str(e)}")
            return False

    def _download_file(self, progress_callback: Callable, stop_flag: threading.Event) -> bool:
        """核心下载方法"""
        try:
            download_url = f"{self.base_url}{self.update_info['download_url']}"
            logger.info(f"开始下载: {download_url}")

            response = requests.get(download_url, stream=True, timeout=30)
            response.raise_for_status()

            # 获取文件名
            content_disposition = response.headers.get('content-disposition', '')
            server_filename = _get_filename_from_cd(content_disposition)
            if not server_filename:
                server_filename = os.path.basename(download_url.split('?')[0])

            logger.info(f"服务器返回文件名: {server_filename}")

            # 确定保存路径
            exe_name = os.path.basename(self._main_program_path)
            if server_filename.lower() == exe_name.lower() and _is_exe():
                # 同名exe文件，保存为.new文件用于延迟替换
                save_path = _get_update_file_path()
                self._is_self_update = True
            else:
                # 不同名文件，保存到当前目录
                save_path = os.path.join(os.getcwd(), server_filename)
                self._is_self_update = False

            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0

            logger.info(f"开始下载到: {save_path}, 文件大小: {total_size} 字节")

            try:
                with open(save_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if stop_flag.is_set():
                            logger.warning("下载被中断")
                            f.close()
                            try:
                                os.remove(save_path)
                                logger.info(f"已删除未完成的下载文件: {save_path}")
                            except Exception:
                                pass
                            return False

                        if chunk:
                            f.write(chunk)
                            downloaded_size += len(chunk)
                            if progress_callback:
                                try:
                                    progress_callback(downloaded_size, total_size)
                                except Exception:
                                    pass

                # 验证文件完整性
                if total_size > 0 and os.path.getsize(save_path) != total_size:
                    try:
                        os.remove(save_path)
                    except Exception:
                        pass
                    raise Exception(f"下载文件不完整 (实际: {os.path.getsize(save_path)}, 预期: {total_size})")

                logger.info(f"下载完成: {save_path}")
                self._downloaded_file_path = save_path
                return True

            except Exception as e:
                # 清理未完成的文件
                if os.path.exists(save_path):
                    try:
                        os.remove(save_path)
                        logger.info(f"下载出错，已删除未完成的文件: {save_path}")
                    except Exception:
                        pass
                raise e

        except Exception as e:
            logger.error(f"下载失败: {str(e)}")
            return False

    def _handle_download_complete(self) -> None:
        """处理下载完成后的操作"""
        try:
            import tkinter as tk
            from tkinter import messagebox

            if self._is_self_update and _is_exe():
                # 自更新exe文件，询问是否重启
                user_restart = messagebox.askyesno(
                    self.software_name,
                    "新版本已下载，是否立即重启更新？\n\n此操作将关闭当前程序并自动完成替换。"
                )

                if user_restart:
                    self._restart_for_update()
                else:
                    messagebox.showinfo(
                        self.software_name,
                        f"更新文件已保存，下次启动时将自动更新。"
                    )
            else:
                # 非自更新文件，显示保存位置
                messagebox.showinfo(
                    self.software_name,
                    f"文件已下载到: {os.path.basename(self._downloaded_file_path)}"
                )

        except Exception as e:
            logger.error(f"处理下载完成失败: {str(e)}")

    def _restart_for_update(self) -> None:
        """重启程序进行更新"""
        try:
            temp_dir = tempfile.mkdtemp()
            batch_file = os.path.join(temp_dir, "update.bat")
            exe_dir = os.path.dirname(self._main_program_path)

            with open(batch_file, "w", encoding="utf-8") as f:
                f.write("@echo off\n")
                f.write(f'cd /d "{exe_dir}"\n')
                f.write("echo 正在更新，请稍候...\n")
                f.write("timeout /t 2 /nobreak > nul\n")
                f.write(f'copy /Y "{self._downloaded_file_path}" "{self._main_program_path}"\n')
                f.write(f'del /f /q "{self._downloaded_file_path}"\n')
                f.write(f'start "" "{self._main_program_path}"\n')
                f.write(f'rmdir /S /Q "{temp_dir}"\n')

            logger.info(f"执行更新批处理: {batch_file}")
            subprocess.Popen(
                ["cmd", "/c", batch_file],
                shell=True,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            logger.info("已启动新程序，当前进程即将退出")
            os._exit(0)

        except Exception as e:
            logger.error(f"重启更新失败: {str(e)}")


# 便利函数，保持向后兼容
def check_update_once(software_name: str, current_version: str, base_url: str = "https://unlockoko.com") -> bool:
    """
    手动检查一次更新（便利函数）

    Args:
        software_name: 软件名称
        current_version: 当前版本号
        base_url: 服务器地址

    Returns:
        True: 检查到更新并显示了通知
        False: 无更新或检查失败
    """
    _try_delayed_replace(software_name)
    updater = UpdateManager(software_name, current_version, base_url)
    return updater.check_once()


def start_update_checker(
    software_name: str,
    current_version: str,
    base_url: str = "https://unlockoko.com",
    check_interval: int = 3600
) -> UpdateManager:
    """
    启动后台更新检查（便利函数）

    Args:
        software_name: 软件名称
        current_version: 当前版本号
        base_url: 服务器地址
        check_interval: 检查间隔（秒）

    Returns:
        UpdateManager实例
    """
    _try_delayed_replace(software_name)
    updater = UpdateManager(software_name, current_version, base_url, check_interval)
    updater.start_background_check()
    return updater


# 向后兼容的别名
UpdateChecker = UpdateManager


if __name__ == "__main__":
    # 示例用法
    updater = start_update_checker("imei_tool", "1.7")

    # 或者手动检查一次
    # check_update_once("imei_tool", "1.7")