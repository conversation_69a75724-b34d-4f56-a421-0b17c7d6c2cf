"""
专业级软件更新模块
支持Windows/Linux/macOS跨平台GUI更新

使用方法：
    from update import UpdateManager

    # 启动后台检查
    updater = UpdateManager("your_software", "1.0.0")

    # 手动检查一次
    updater.check_once()
"""

import os
import sys
import time
import threading
import subprocess
import tempfile
import requests
import logging
import atexit
import re
import shutil
import zipfile
import platform
from pathlib import Path
from typing import Optional, Dict, Any

# 简洁的日志配置 - 只输出到控制台
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('UpdateManager')

# 平台检测
PLATFORM = platform.system().lower()
IS_WINDOWS = PLATFORM == 'windows'


# 工具函数
def _get_filename_from_cd(content_disposition: str) -> Optional[str]:
    """从Content-Disposition头中提取文件名"""
    if not content_disposition:
        return None
    fname = re.findall(r'filename=["\'](.*?)["\']|filename=([^;]+)', content_disposition)
    if fname:
        for groups in fname:
            for group in groups:
                if group:
                    return group.strip()
    return None


def _get_app_directory() -> Path:
    """获取应用程序目录"""
    if getattr(sys, 'frozen', False):
        return Path(sys.executable).parent
    else:
        return Path(sys.argv[0]).parent.resolve()


def _is_zip_file(filename: str) -> bool:
    """检查是否为ZIP文件"""
    return filename.lower().endswith('.zip')


def _is_same_file(filename: str) -> bool:
    """检查是否为同名文件"""
    current_name = Path(sys.argv[0]).name
    return filename.lower() == current_name.lower()


def _is_frozen() -> bool:
    """检查是否为打包后的可执行文件"""
    return getattr(sys, 'frozen', False)


def _try_delayed_replace(software_name: str = "软件更新") -> None:
    """检查并执行延迟替换"""
    app_dir = _get_app_directory()
    current_exe = Path(sys.argv[0])
    new_file = app_dir / f"{current_exe.name}.new"

    if new_file.exists() and _is_frozen():
        logger.info(f"检测到延迟升级文件，准备替换...")
        try:
            if new_file.stat().st_size == 0:
                new_file.unlink(missing_ok=True)
                return

            _create_replace_script(current_exe, new_file, software_name)

        except Exception as e:
            logger.error(f"延迟升级失败: {str(e)}")


def _create_replace_script(current_exe: Path, new_file: Path, software_name: str) -> None:
    """创建替换脚本"""
    temp_dir = Path(tempfile.mkdtemp())

    try:
        if IS_WINDOWS:
            script_file = temp_dir / "replace.bat"
            with open(script_file, "w", encoding="utf-8") as f:
                f.write("@echo off\n")
                f.write("echo 正在更新...\n")
                f.write("timeout /t 2 /nobreak > nul\n")
                f.write(f'copy /Y "{new_file}" "{current_exe}"\n')
                f.write(f'del /f /q "{new_file}"\n')
                f.write(f'start "" "{current_exe}"\n')
                f.write(f'rmdir /S /Q "{temp_dir}"\n')

            subprocess.Popen(["cmd", "/c", str(script_file)],
                           creationflags=subprocess.CREATE_NO_WINDOW)
        else:
            script_file = temp_dir / "replace.sh"
            with open(script_file, "w", encoding="utf-8") as f:
                f.write("#!/bin/bash\n")
                f.write("echo '正在更新...'\n")
                f.write("sleep 2\n")
                f.write(f"cp '{new_file}' '{current_exe}'\n")
                f.write(f"chmod +x '{current_exe}'\n")
                f.write(f"rm -f '{new_file}'\n")
                f.write(f"'{current_exe}' &\n")
                f.write(f"rm -rf '{temp_dir}'\n")

            script_file.chmod(0o755)
            subprocess.Popen(["/bin/bash", str(script_file)])

        os._exit(0)

    except Exception as e:
        logger.error(f"创建替换脚本失败: {str(e)}")
        shutil.rmtree(temp_dir, ignore_errors=True)


class UpdateManager:
    """专业级更新管理器"""

    def __init__(
        self,
        software_name: str,
        current_version: str,
        base_url: str = "https://unlockoko.com",
        check_interval: int = 3600
    ):
        """初始化更新管理器"""
        self.software_name = software_name.lower()
        self.current_version = current_version
        self.base_url = base_url.rstrip('/')
        self.check_interval = check_interval
        self.update_info: Optional[Dict[str, Any]] = None
        self.running = True
        self.thread: Optional[threading.Thread] = None

        # 下载状态
        self._download_window = None
        self._download_cancelled = False
        self._download_stop_event = None

        logger.info(f"初始化更新管理器: {software_name} v{current_version}")
        atexit.register(self.stop_background_check)

        # 自动启动后台检查
        self.start_background_check()

    def start_background_check(self) -> None:
        """启动后台检查"""
        if self.thread is None or not self.thread.is_alive():
            self.running = True
            self.thread = threading.Thread(target=self._check_periodically, daemon=True)
            self.thread.start()

    def stop_background_check(self) -> None:
        """停止后台检查"""
        self.running = False
        self._cancel_download()

    def _cancel_download(self) -> None:
        """取消下载"""
        self._download_cancelled = True
        if self._download_stop_event:
            self._download_stop_event.set()
        if self._download_window:
            try:
                self._download_window.quit()
                self._download_window.destroy()
                self._download_window = None
            except Exception:
                pass

    def _check_periodically(self) -> None:
        """定时检查循环"""
        while self.running:
            try:
                if self.check_update():
                    self._show_update_notification()
            except Exception as e:
                logger.error(f"检查更新失败: {str(e)}")

            # 等待下次检查
            for _ in range(self.check_interval):
                if not self.running:
                    break
                time.sleep(1)

    def check_update(self) -> bool:
        """检查是否有更新"""
        try:
            url = f"{self.base_url}/api/check_update"
            params = {
                "software_name": self.software_name,
                "current_version": self.current_version
            }

            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()

            if data.get("code") == "100":
                update_data = data.get("data", {})
                need_update = update_data.get("need_update", False)

                if need_update:
                    self.update_info = {
                        "version": update_data.get("version", ""),
                        "download_url": update_data.get("download_url", ""),
                        "release_notes": update_data.get("release_notes", "")
                    }
                    logger.info(f"发现新版本: {self.update_info['version']}")
                    return True
                else:
                    return False
            else:
                logger.warning(f"服务器错误: {data.get('msg', '未知错误')}")
                return False

        except Exception as e:
            logger.error(f"检查更新失败: {str(e)}")
            return False

    def check_once(self) -> bool:
        """手动检查一次更新"""
        _try_delayed_replace(self.software_name)

        if self.check_update():
            self._show_update_notification()
            return True
        return False

    def _show_update_notification(self) -> None:
        """显示更新通知"""
        try:
            import tkinter as tk
            from tkinter import messagebox

            # 创建根窗口
            root = tk.Tk()
            root.withdraw()
            root.title(f"{self.software_name} - 更新通知")
            root.attributes('-topmost', True)

            if IS_WINDOWS:
                root.attributes('-toolwindow', True)

            # 构建消息
            message = (
                f"发现新版本 {self.update_info['version']}\n\n"
                f"当前版本: {self.current_version}\n\n"
                f"更新说明:\n{self.update_info['release_notes']}\n\n"
                "是否现在下载并安装更新?"
            )

            user_choice = messagebox.askyesno(
                f"{self.software_name} - 发现新版本",
                message,
                parent=root
            )

            root.destroy()

            if user_choice:
                self._start_download()

        except ImportError:
            self._show_console_notification()
        except Exception as e:
            logger.error(f"显示更新通知失败: {str(e)}")
            self._show_console_notification()

    def _show_console_notification(self) -> None:
        """控制台通知"""
        print(f"\n发现新版本: {self.update_info['version']}")
        print(f"当前版本: {self.current_version}")
        print(f"更新说明: {self.update_info['release_notes']}")

        try:
            choice = input("是否下载更新? (y/n): ").lower().strip()
            if choice in ['y', 'yes']:
                self._start_download()
        except (EOFError, KeyboardInterrupt):
            print("取消更新")

    def _start_download(self) -> None:
        """启动下载"""
        if not self.update_info or not self.update_info.get("download_url"):
            logger.error("没有可用的更新信息")
            return

        self._download_cancelled = False
        self._download_stop_event = threading.Event()

        try:
            import tkinter as tk
            from tkinter import ttk, messagebox

            # 创建下载窗口
            self._download_window = tk.Tk()
            self._download_window.title(f"{self.software_name} - 正在下载")
            self._download_window.geometry("450x150")
            self._download_window.resizable(False, False)
            self._download_window.attributes('-topmost', True)

            if IS_WINDOWS:
                self._download_window.attributes('-toolwindow', True)

            # 居中显示
            screen_width = self._download_window.winfo_screenwidth()
            screen_height = self._download_window.winfo_screenheight()
            x = (screen_width - 450) // 2
            y = (screen_height - 150) // 2
            self._download_window.geometry(f"450x150+{x}+{y}")

            # 创建界面
            frame = ttk.Frame(self._download_window, padding="20")
            frame.pack(fill=tk.BOTH, expand=True)

            # 标题
            ttk.Label(frame, text=f"正在下载 {self.update_info['version']}").pack(pady=(0, 10))

            # 进度条
            self._progress_var = tk.DoubleVar()
            self._progress_bar = ttk.Progressbar(frame, variable=self._progress_var, length=400)
            self._progress_bar.pack(pady=(0, 10))

            # 状态标签
            self._status_label = ttk.Label(frame, text="准备下载...")
            self._status_label.pack(pady=(0, 10))

            # 取消按钮
            ttk.Button(frame, text="取消", command=self._on_cancel).pack()

            # 设置关闭事件
            self._download_window.protocol("WM_DELETE_WINDOW", self._on_cancel)

            # 启动下载线程
            threading.Thread(target=self._download_file, daemon=True).start()

            # 运行GUI
            self._download_window.mainloop()

        except ImportError:
            self._download_console()
        except Exception as e:
            logger.error(f"创建下载界面失败: {str(e)}")
            self._download_console()

    def _create_download_gui(self) -> None:
        """创建专业级下载GUI界面"""
        try:
            import tkinter as tk
            from tkinter import ttk, messagebox

            # 创建下载窗口
            self._download_window = tk.Tk()
            self._download_window.title(f"{self.software_name} - 正在更新")
            self._download_window.geometry("500x200")
            self._download_window.resizable(False, False)
            self._download_window.attributes('-topmost', True)

            if IS_WINDOWS:
                self._download_window.attributes('-toolwindow', True)

            # 居中显示
            self._center_window(self._download_window, 500, 200)

            # 创建界面元素
            main_frame = ttk.Frame(self._download_window, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 标题
            title_label = ttk.Label(
                main_frame,
                text=f"正在下载 {self.software_name} v{self.update_info['version']}",
                font=('Arial', 12, 'bold')
            )
            title_label.pack(pady=(0, 15))

            # 进度条
            self._progress_var = tk.DoubleVar()
            progress_bar = ttk.Progressbar(
                main_frame,
                variable=self._progress_var,
                maximum=100,
                length=450,
                mode='determinate'
            )
            progress_bar.pack(pady=(0, 10))

            # 状态标签
            self._status_label = ttk.Label(main_frame, text="准备下载...")
            self._status_label.pack(pady=(0, 10))

            # 详细信息标签
            self._detail_label = ttk.Label(main_frame, text="", foreground="gray")
            self._detail_label.pack(pady=(0, 15))

            # 取消按钮
            self._cancel_button = ttk.Button(
                main_frame,
                text="取消下载",
                command=self._on_cancel_download
            )
            self._cancel_button.pack()

            # 设置关闭事件
            self._download_window.protocol("WM_DELETE_WINDOW", self._on_window_close)

            # 启动下载线程
            self._download_thread = threading.Thread(target=self._execute_download, daemon=True)
            self._download_thread.start()

            # 运行GUI主循环
            self._download_window.mainloop()

        except ImportError:
            logger.error("tkinter不可用，使用控制台下载")
            self._download_with_console()
        except Exception as e:
            logger.error(f"创建下载GUI失败: {str(e)}")
            self._download_with_console()

    def _center_window(self, window, width: int, height: int) -> None:
        """居中显示窗口"""
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        x = int(screen_width / 2 - width / 2)
        y = int(screen_height / 2 - height / 2)
        window.geometry(f"{width}x{height}+{x}+{y}")

    def _on_cancel_download(self) -> None:
        """处理取消下载按钮点击"""
        try:
            import tkinter as tk
            from tkinter import messagebox

            if messagebox.askokcancel(
                "取消下载",
                "确定要取消下载吗？",
                parent=self._download_window
            ):
                self._perform_download_cancellation()
        except Exception as e:
            logger.error(f"处理取消下载失败: {str(e)}")

    def _on_window_close(self) -> None:
        """处理窗口关闭事件"""
        try:
            import tkinter as tk
            from tkinter import messagebox

            if messagebox.askokcancel(
                "取消下载",
                "正在下载中，确定要关闭窗口吗？",
                parent=self._download_window
            ):
                self._perform_download_cancellation()
        except Exception as e:
            logger.error(f"处理窗口关闭失败: {str(e)}")

    def _perform_download_cancellation(self) -> None:
        """执行下载取消操作"""
        try:
            logger.warning("用户取消了下载")

            # 设置取消标志
            self._download_cancelled = True

            # 停止下载线程
            if self._download_stop_event:
                self._download_stop_event.set()

            # 更新UI状态
            if self._download_window and hasattr(self, '_status_label'):
                self._status_label.config(text="正在取消下载...")
                self._cancel_button.config(state='disabled', text="取消中...")
                self._download_window.update()

            # 等待下载线程结束（最多3秒）
            if self._download_thread and self._download_thread.is_alive():
                self._download_thread.join(timeout=3)

            # 关闭窗口
            if self._download_window:
                self._download_window.quit()
                self._download_window.destroy()
                self._download_window = None

            logger.info("下载已成功取消")

        except Exception as e:
            logger.error(f"执行下载取消失败: {str(e)}")
            # 强制关闭窗口
            if self._download_window:
                try:
                    self._download_window.destroy()
                    self._download_window = None
                except Exception:
                    pass

    def _update_download_progress(self, current: int, total: int) -> None:
        """更新下载进度"""
        try:
            if self._download_window and hasattr(self, '_progress_var'):
                percentage = (current / total * 100) if total > 0 else 0
                self._progress_var.set(percentage)

                # 更新状态文本
                if total > 0:
                    current_mb = current / 1024 / 1024
                    total_mb = total / 1024 / 1024
                    speed_text = f"{current_mb:.1f} MB / {total_mb:.1f} MB ({percentage:.1f}%)"
                    self._status_label.config(text=speed_text)

                    # 更新详细信息
                    if hasattr(self, '_download_start_time'):
                        elapsed = time.time() - self._download_start_time
                        if elapsed > 0:
                            speed = current / elapsed / 1024 / 1024  # MB/s
                            remaining = (total - current) / (current / elapsed) if current > 0 else 0
                            detail_text = f"速度: {speed:.1f} MB/s, 剩余时间: {remaining:.0f}秒"
                            self._detail_label.config(text=detail_text)

                self._download_window.update_idletasks()
        except Exception as e:
            logger.debug(f"更新进度失败: {str(e)}")

    def _execute_download(self) -> None:
        """执行下载任务"""
        try:
            self._download_start_time = time.time()
            success = self._download_file_with_progress()

            if success and not self._download_cancelled:
                # 下载成功，处理安装
                self._download_window.after_idle(self._handle_download_success)
            elif not self._download_cancelled:
                # 下载失败
                self._download_window.after_idle(self._handle_download_error)

        except Exception as e:
            logger.error(f"执行下载失败: {str(e)}")
            if not self._download_cancelled:
                self._download_window.after_idle(lambda: self._handle_download_error(str(e)))

    def _download_with_console(self) -> bool:
        """控制台模式下载"""
        def console_progress(current, total):
            if total > 0:
                percent = current / total * 100
                bar_length = 50
                filled_length = int(bar_length * current // total)
                bar = '█' * filled_length + '-' * (bar_length - filled_length)
                print(f'\r下载进度: |{bar}| {percent:.1f}% ({current/1024/1024:.1f}MB/{total/1024/1024:.1f}MB)', end='')
                if current == total:
                    print()  # 换行

        return self._download_file_with_progress(console_progress)

    def _download_file_with_progress(self, progress_callback: Optional[Callable] = None) -> bool:
        """核心下载方法 - 支持ZIP和跨平台，正确处理文件类型"""
        try:
            download_url = f"{self.base_url}{self.update_info['download_url']}"
            logger.info(f"开始下载: {download_url}")

            response = requests.get(download_url, stream=True, timeout=30)
            response.raise_for_status()

            # 获取文件名
            content_disposition = response.headers.get('content-disposition', '')
            server_filename = _get_filename_from_cd(content_disposition)
            if not server_filename:
                server_filename = os.path.basename(download_url.split('?')[0])

            logger.info(f"服务器返回文件名: {server_filename}")

            # 确定保存路径和更新类型
            save_path = _get_temp_update_path(server_filename)
            self._downloaded_file_path = save_path
            self._is_zip_update = _is_zip_file(server_filename)

            # 智能判断更新类型
            self._determine_update_type(server_filename)

            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0

            logger.info(f"开始下载到: {save_path}")
            logger.info(f"文件类型: {'ZIP包' if self._is_zip_update else '单文件'}")
            logger.info(f"更新类型: {self._get_update_type_description()}")

            try:
                with open(save_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        # 检查取消标志
                        if self._download_cancelled or (self._download_stop_event and self._download_stop_event.is_set()):
                            logger.warning("下载被取消")
                            f.close()
                            save_path.unlink(missing_ok=True)
                            return False

                        if chunk:
                            f.write(chunk)
                            downloaded_size += len(chunk)

                            # 更新进度
                            if progress_callback:
                                try:
                                    progress_callback(downloaded_size, total_size)
                                except Exception:
                                    pass

                            # GUI进度更新
                            if self._download_window and not self._download_cancelled:
                                try:
                                    self._download_window.after_idle(
                                        lambda c=downloaded_size, t=total_size: self._update_download_progress(c, t)
                                    )
                                except Exception:
                                    pass

                # 验证文件完整性
                if total_size > 0 and save_path.stat().st_size != total_size:
                    save_path.unlink(missing_ok=True)
                    raise Exception(f"下载文件不完整 (实际: {save_path.stat().st_size}, 预期: {total_size})")

                logger.info(f"下载完成: {save_path}")
                return True

            except Exception as e:
                # 清理未完成的文件
                if save_path.exists():
                    save_path.unlink(missing_ok=True)
                    logger.info(f"下载出错，已删除未完成的文件: {save_path}")
                raise e

        except Exception as e:
            logger.error(f"下载失败: {str(e)}")
            return False

    def _determine_update_type(self, server_filename: str) -> None:
        """智能判断更新类型"""
        current_name = self._current_executable.name

        if self._is_zip_update:
            # ZIP包更新
            self._is_self_update = False
            self._update_type = "zip_package"
        elif server_filename.lower() == current_name.lower():
            # 同名文件
            if self._is_frozen:
                # 打包后的exe，可以自更新
                self._is_self_update = True
                self._update_type = "self_update_exe"
            else:
                # Python脚本运行，下载的是exe
                self._is_self_update = False
                self._update_type = "manual_install"
        else:
            # 不同名文件
            self._is_self_update = False
            if server_filename.lower().endswith('.exe') and self._is_python_script:
                self._update_type = "python_to_exe"
            else:
                self._update_type = "different_file"

    def _get_update_type_description(self) -> str:
        """获取更新类型描述"""
        descriptions = {
            "zip_package": "ZIP包更新（立即生效）",
            "self_update_exe": "自更新（重启生效）",
            "manual_install": "手动安装（需要用户操作）",
            "python_to_exe": "Python脚本下载exe（手动运行）",
            "different_file": "普通文件下载"
        }
        return descriptions.get(getattr(self, '_update_type', 'unknown'), "未知类型")

    def _handle_download_success(self) -> None:
        """处理下载成功 - 根据更新类型智能处理"""
        try:
            # 更新状态
            if self._download_window and hasattr(self, '_status_label'):
                self._status_label.config(text="下载完成，准备安装...")
                self._detail_label.config(text="正在处理更新文件...")
                self._download_window.update()

            # 根据更新类型处理
            update_type = getattr(self, '_update_type', 'unknown')

            if update_type == "zip_package":
                # ZIP包更新 - 立即安装
                success = self._install_zip_update()
                if success:
                    self._show_zip_install_success_dialog()
                else:
                    self._show_install_error_dialog("ZIP包安装失败")

            elif update_type == "self_update_exe":
                # exe自更新 - 询问重启
                self._show_restart_dialog()

            elif update_type == "python_to_exe":
                # Python脚本下载exe - 提示手动运行
                self._show_python_to_exe_dialog()

            elif update_type == "manual_install":
                # 需要手动安装
                self._show_manual_install_dialog()

            else:
                # 普通文件下载
                self._show_download_complete_dialog()

        except Exception as e:
            logger.error(f"处理下载成功失败: {str(e)}")
            self._show_install_error_dialog(str(e))

    def _handle_download_error(self, error_msg: str = "下载失败") -> None:
        """处理下载错误"""
        try:
            import tkinter as tk
            from tkinter import messagebox

            messagebox.showerror(
                f"{self.software_name} - 下载失败",
                f"下载更新时发生错误:\n\n{error_msg}",
                parent=self._download_window
            )
            self._download_window.destroy()

        except Exception as e:
            logger.error(f"处理下载错误失败: {str(e)}")

    def _install_zip_update(self) -> bool:
        """安装ZIP包更新"""
        try:
            logger.info("开始安装ZIP包更新...")

            # 创建临时解压目录
            temp_extract_dir = self._app_directory / f"temp_extract_{int(time.time())}"
            temp_extract_dir.mkdir(exist_ok=True)

            try:
                # 解压ZIP文件
                with zipfile.ZipFile(self._downloaded_file_path, 'r') as zip_ref:
                    zip_ref.extractall(temp_extract_dir)

                logger.info(f"ZIP文件已解压到: {temp_extract_dir}")

                # 备份当前文件
                backup_dir = self._create_backup()

                # 复制新文件到应用目录
                self._copy_update_files(temp_extract_dir, self._app_directory)

                # 设置可执行权限（Linux/macOS）
                if not IS_WINDOWS:
                    self._set_executable_permissions()

                logger.info("ZIP包更新安装完成")
                return True

            finally:
                # 清理临时文件
                shutil.rmtree(temp_extract_dir, ignore_errors=True)
                self._downloaded_file_path.unlink(missing_ok=True)

        except Exception as e:
            logger.error(f"安装ZIP包更新失败: {str(e)}")
            return False

    def _create_backup(self) -> Path:
        """创建当前版本的备份"""
        try:
            backup_dir = self._app_directory / f"backup_{int(time.time())}"
            backup_dir.mkdir(exist_ok=True)

            # 备份主要文件
            for item in self._app_directory.iterdir():
                if item.name.startswith(('backup_', 'temp_', 'update_')):
                    continue

                if item.is_file():
                    shutil.copy2(item, backup_dir / item.name)
                elif item.is_dir() and not item.name.startswith('.'):
                    shutil.copytree(item, backup_dir / item.name, ignore_errors=True)

            logger.info(f"已创建备份: {backup_dir}")
            return backup_dir

        except Exception as e:
            logger.warning(f"创建备份失败: {str(e)}")
            return None

    def _copy_update_files(self, source_dir: Path, target_dir: Path) -> None:
        """复制更新文件到目标目录"""
        for item in source_dir.rglob('*'):
            if item.is_file():
                # 计算相对路径
                rel_path = item.relative_to(source_dir)
                target_path = target_dir / rel_path

                # 确保目标目录存在
                target_path.parent.mkdir(parents=True, exist_ok=True)

                # 复制文件
                shutil.copy2(item, target_path)
                logger.debug(f"已复制: {rel_path}")

    def _set_executable_permissions(self) -> None:
        """设置可执行文件权限（Linux/macOS）"""
        if IS_WINDOWS:
            return

        try:
            # 设置主程序可执行权限
            _make_executable(self._current_executable)

            # 查找并设置其他可执行文件权限
            for item in self._app_directory.rglob('*'):
                if item.is_file() and not item.suffix:
                    # 无扩展名的文件可能是可执行文件
                    try:
                        _make_executable(item)
                    except Exception:
                        pass

        except Exception as e:
            logger.warning(f"设置可执行权限失败: {str(e)}")

    def _show_restart_dialog(self) -> None:
        """显示重启对话框"""
        try:
            import tkinter as tk
            from tkinter import messagebox

            self._download_window.destroy()

            user_restart = messagebox.askyesno(
                f"{self.software_name} - 更新完成",
                "新版本已下载完成！\n\n是否立即重启应用以完成更新？\n\n"
                "选择'是'将关闭当前程序并自动完成更新。\n"
                "选择'否'将在下次启动时自动更新。"
            )

            if user_restart:
                self._restart_for_update()
            else:
                messagebox.showinfo(
                    f"{self.software_name} - 更新提示",
                    "更新文件已准备就绪，下次启动时将自动完成更新。"
                )

        except Exception as e:
            logger.error(f"显示重启对话框失败: {str(e)}")

    def _show_zip_install_success_dialog(self) -> None:
        """显示ZIP包安装成功对话框"""
        try:
            import tkinter as tk
            from tkinter import messagebox

            if self._download_window:
                self._download_window.destroy()
                self._download_window = None

            messagebox.showinfo(
                f"{self.software_name} - 更新完成",
                f"🎉 ZIP包更新安装成功！\n\n"
                f"版本: {self.update_info['version']}\n"
                f"更新已立即生效，无需重启。"
            )

        except Exception as e:
            logger.error(f"显示ZIP安装成功对话框失败: {str(e)}")

    def _show_python_to_exe_dialog(self) -> None:
        """显示Python脚本下载exe的对话框"""
        try:
            import tkinter as tk
            from tkinter import messagebox

            if self._download_window:
                self._download_window.destroy()
                self._download_window = None

            file_name = self._downloaded_file_path.name

            messagebox.showinfo(
                f"{self.software_name} - 下载完成",
                f"✅ 新版本exe文件下载完成！\n\n"
                f"文件名: {file_name}\n"
                f"版本: {self.update_info['version']}\n\n"
                f"💡 由于您当前运行的是Python脚本，\n"
                f"请手动运行下载的exe文件来使用新版本。\n\n"
                f"文件已保存到当前目录。"
            )

        except Exception as e:
            logger.error(f"显示Python转exe对话框失败: {str(e)}")

    def _show_manual_install_dialog(self) -> None:
        """显示手动安装对话框"""
        try:
            import tkinter as tk
            from tkinter import messagebox

            if self._download_window:
                self._download_window.destroy()
                self._download_window = None

            file_name = self._downloaded_file_path.name

            messagebox.showinfo(
                f"{self.software_name} - 下载完成",
                f"📥 更新文件下载完成！\n\n"
                f"文件名: {file_name}\n"
                f"版本: {self.update_info['version']}\n\n"
                f"⚠️ 需要手动安装更新：\n"
                f"请手动替换或安装下载的文件。\n\n"
                f"文件已保存到当前目录。"
            )

        except Exception as e:
            logger.error(f"显示手动安装对话框失败: {str(e)}")

    def _show_download_complete_dialog(self) -> None:
        """显示下载完成对话框"""
        try:
            import tkinter as tk
            from tkinter import messagebox

            self._download_window.destroy()

            messagebox.showinfo(
                f"{self.software_name} - 下载完成",
                f"文件下载完成！\n\n"
                f"保存位置: {self._downloaded_file_path.name}\n"
                f"请手动安装更新。"
            )

        except Exception as e:
            logger.error(f"显示下载完成对话框失败: {str(e)}")

    def _show_install_error_dialog(self, error_msg: str) -> None:
        """显示安装错误对话框"""
        try:
            import tkinter as tk
            from tkinter import messagebox

            self._download_window.destroy()

            messagebox.showerror(
                f"{self.software_name} - 安装失败",
                f"更新安装时发生错误:\n\n{error_msg}\n\n"
                f"请尝试手动安装或联系技术支持。"
            )

        except Exception as e:
            logger.error(f"显示安装错误对话框失败: {str(e)}")

    def _restart_for_update(self) -> None:
        """跨平台重启程序进行更新"""
        try:
            _create_and_run_replace_script(
                self._current_executable,
                self._downloaded_file_path,
                self.software_name
            )
        except Exception as e:
            logger.error(f"重启更新失败: {str(e)}")


# 便利函数 - 专业级GUI模式
def check_update_once(software_name: str, current_version: str, base_url: str = "https://unlockoko.com") -> bool:
    """
    手动检查一次更新（专业级GUI模式）

    Args:
        software_name: 软件名称
        current_version: 当前版本号
        base_url: 服务器地址

    Returns:
        True: 检查到更新并显示了通知
        False: 无更新或检查失败
    """
    _try_delayed_replace(software_name)
    updater = UpdateManager(software_name, current_version, base_url, auto_check=False)
    return updater.check_once()


def start_update_checker(
    software_name: str,
    current_version: str,
    base_url: str = "https://unlockoko.com",
    check_interval: int = 3600
) -> UpdateManager:
    """
    启动后台更新检查（专业级GUI模式）

    Args:
        software_name: 软件名称
        current_version: 当前版本号
        base_url: 服务器地址
        check_interval: 检查间隔（秒）

    Returns:
        UpdateManager实例
    """
    _try_delayed_replace(software_name)
    updater = UpdateManager(software_name, current_version, base_url, check_interval, auto_check=True)
    return updater


# 向后兼容的别名
UpdateChecker = UpdateManager


if __name__ == "__main__":
    # 示例用法 - 专业级GUI模式
    print(f"专业级更新模块启动 [{PLATFORM}]")

    # 方式1: 自动启动后台检查
    updater = UpdateManager("imei_tool", "1.7")

    # 方式2: 手动检查一次
    # check_update_once("imei_tool", "1.7")

    # 方式3: 使用便利函数
    # updater = start_update_checker("imei_tool", "1.7")

    try:
        # 保持程序运行以测试后台检查
        import time
        time.sleep(10)
    except KeyboardInterrupt:
        print("程序退出")
        updater.stop_background_check()